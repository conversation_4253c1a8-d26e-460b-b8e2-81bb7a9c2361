"""
Tushare数据源配置
"""
import os
import json
from typing import Optional


class TushareConfig:
    """Tushare配置类"""

    def __init__(self):
        self.config_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'config',
            'tushare_token.json'
        )

        # 从配置文件加载token，如果没有则尝试环境变量
        self.token = self._load_token()

        # API配置
        self.api_timeout = 30  # API超时时间（秒）
        self.retry_times = 3   # 重试次数
        self.retry_delay = 1   # 重试延时（秒）

        # 请求频率控制
        self.request_delay_range = (0.1, 0.3)  # 随机延时范围（秒）
        self.max_requests_per_minute = 200     # 每分钟最大请求数

        # 数据获取配置
        self.batch_size = 50   # 批量处理大小
        self.max_workers = 3   # 并发线程数

    def _load_token(self) -> str:
        """从配置文件加载token"""
        try:
            # 首先尝试从配置文件加载
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('token', '')
        except Exception:
            pass

        # 如果配置文件不存在或读取失败，尝试环境变量
        return os.getenv('TUSHARE_TOKEN', '')

    def _save_token(self, token: str) -> bool:
        """保存token到配置文件"""
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            os.makedirs(config_dir, exist_ok=True)

            # 保存配置
            config = {'token': token}
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            return True
        except Exception as e:
            print(f"保存token失败: {str(e)}")
            return False

    def get_token(self) -> str:
        """获取tushare token"""
        if not self.token:
            raise ValueError(
                "Tushare token未配置。请使用 set_token() 方法设置token或运行配置脚本"
            )
        return self.token

    def set_token(self, token: str) -> bool:
        """设置tushare token并保存到配置文件"""
        if not token or not isinstance(token, str):
            raise ValueError("Token不能为空且必须是字符串")

        self.token = token
        return self._save_token(token)

    def is_token_configured(self) -> bool:
        """检查token是否已配置"""
        return bool(self.token)

    def get_config_file_path(self) -> str:
        """获取配置文件路径"""
        return self.config_file


# 全局配置实例
tushare_config = TushareConfig()
