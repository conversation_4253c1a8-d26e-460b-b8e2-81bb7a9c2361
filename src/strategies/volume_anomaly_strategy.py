"""
交易量异动选股策略
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict
from statistics import mean

from ..core.interfaces.strategy import ISelectionStrategy
from ..core.interfaces.data_access import IDataAccess
from ..utils.date_utils import get_trading_days


class VolumeAnomalyStrategy(ISelectionStrategy):
    """交易量异动选股策略"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = {
            'volume_multiplier_threshold': 2.0,  # 交易量放大倍数阈值
            'min_price': 3.0,  # 最低价格
            'max_price': 100.0,  # 最高价格
            'baseline_days': 14,  # 基准期天数（2周）
            'exclude_st': True,  # 排除ST股票
            'min_volume': 1000000,  # 最小交易量（手）
            'max_results': 20  # 最大结果数量
        }
    
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return "交易量异动策略"
    
    def get_strategy_description(self) -> str:
        """获取策略描述"""
        return "基于交易量异动筛选股票，寻找交易量突然放大的股票"
    
    def get_config(self) -> Dict:
        """获取策略配置"""
        return self.config.copy()
    
    def set_config(self, config: Dict) -> None:
        """设置策略配置"""
        self.config.update(config)
    
    def validate_config(self, config: Dict) -> bool:
        """验证策略配置"""
        required_keys = [
            'volume_multiplier_threshold', 'min_price', 'max_price',
            'baseline_days', 'exclude_st', 'min_volume', 'max_results'
        ]
        
        for key in required_keys:
            if key not in config:
                return False
        
        # 验证数值范围
        if config['volume_multiplier_threshold'] <= 1.0:
            return False
        if config['min_price'] <= 0 or config['max_price'] <= config['min_price']:
            return False
        if config['baseline_days'] <= 0:
            return False
        if config['min_volume'] <= 0:
            return False
        if config['max_results'] <= 0:
            return False
        
        return True
    
    def execute(self, data_access: IDataAccess) -> List[Dict]:
        """执行选股策略"""
        try:
            self.logger.info(f"开始执行{self.get_strategy_name()}")
            
            # 获取所有股票代码
            stock_codes = data_access.get_all_stock_codes()
            self.logger.info(f"获取到{len(stock_codes)}只股票")
            
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config['baseline_days'] + 5)  # 多取几天以防节假日
            
            results = []
            processed_count = 0
            
            for stock_code in stock_codes:
                try:
                    # 获取股票基本信息
                    stock_info = data_access.get_stock_info(stock_code)
                    if not stock_info:
                        continue
                    
                    # 过滤ST股票
                    if self.config['exclude_st'] and self._is_st_stock(stock_info['stock_name']):
                        continue
                    
                    # 获取交易数据
                    trading_data = data_access.get_stock_data(stock_code, start_date, end_date)
                    if len(trading_data) < self.config['baseline_days']:
                        continue
                    
                    # 分析交易量异动
                    anomaly_result = self._analyze_volume_anomaly(stock_code, stock_info, trading_data)
                    if anomaly_result:
                        results.append(anomaly_result)
                    
                    processed_count += 1
                    if processed_count % 100 == 0:
                        self.logger.info(f"已处理{processed_count}只股票，当前选中{len(results)}只")
                
                except Exception as e:
                    self.logger.warning(f"处理股票{stock_code}时出错: {str(e)}")
                    continue
            
            # 按评分排序并限制结果数量
            results.sort(key=lambda x: x['score'], reverse=True)
            results = results[:self.config['max_results']]
            
            self.logger.info(f"选股完成，共选中{len(results)}只股票")
            return results
            
        except Exception as e:
            self.logger.error(f"执行选股策略失败: {str(e)}")
            raise
    
    def _is_st_stock(self, stock_name: str) -> bool:
        """判断是否为ST股票"""
        st_keywords = ['ST', '*ST', 'PT']
        return any(keyword in stock_name for keyword in st_keywords)
    
    def _analyze_volume_anomaly(self, stock_code: str, stock_info: Dict, trading_data: List[Dict]) -> Dict:
        """分析交易量异动"""
        try:
            # 按日期排序
            trading_data.sort(key=lambda x: x['trade_date'])
            
            # 获取最新交易日数据
            latest_data = trading_data[-1]
            
            # 价格过滤
            close_price = float(latest_data['close_price'])
            if close_price < self.config['min_price'] or close_price > self.config['max_price']:
                return None
            
            # 交易量过滤
            latest_volume = int(latest_data['volume'])
            if latest_volume < self.config['min_volume']:
                return None
            
            # 计算基准期平均交易量（排除最新一天）
            baseline_data = trading_data[-self.config['baseline_days']-1:-1]
            if len(baseline_data) < self.config['baseline_days']:
                return None
            
            baseline_volumes = [int(data['volume']) for data in baseline_data]
            avg_baseline_volume = mean(baseline_volumes)
            
            if avg_baseline_volume <= 0:
                return None
            
            # 计算交易量放大倍数
            volume_multiplier = latest_volume / avg_baseline_volume
            
            # 检查是否满足阈值
            if volume_multiplier < self.config['volume_multiplier_threshold']:
                return None
            
            # 计算评分（基于放大倍数和价格位置）
            score = self._calculate_score(volume_multiplier, close_price, latest_data)
            
            # 生成选股原因
            reason = f"交易量放大{volume_multiplier:.2f}倍，当前价格{close_price:.2f}元"
            
            return {
                'stock_code': stock_code,
                'stock_name': stock_info['stock_name'],
                'score': score,
                'reason': reason,
                'selection_date': datetime.now().date(),
                'volume_multiplier': volume_multiplier,
                'close_price': close_price,
                'latest_volume': latest_volume,
                'avg_baseline_volume': avg_baseline_volume
            }
            
        except Exception as e:
            self.logger.warning(f"分析股票{stock_code}交易量异动失败: {str(e)}")
            return None
    
    def _calculate_score(self, volume_multiplier: float, close_price: float, latest_data: Dict) -> float:
        """计算选股评分"""
        try:
            # 基础分数：交易量放大倍数
            base_score = min(volume_multiplier * 10, 100)  # 最高100分
            
            # 价格位置加分（相对于当日高低价的位置）
            high_price = float(latest_data['high_price'])
            low_price = float(latest_data['low_price'])
            
            if high_price > low_price:
                price_position = (close_price - low_price) / (high_price - low_price)
                position_bonus = price_position * 10  # 最高10分
            else:
                position_bonus = 5  # 默认5分
            
            # 换手率加分
            turnover_rate = float(latest_data.get('turnover_rate', 0))
            turnover_bonus = min(turnover_rate * 2, 20)  # 最高20分
            
            total_score = base_score + position_bonus + turnover_bonus
            return round(total_score, 2)
            
        except Exception as e:
            self.logger.warning(f"计算评分失败: {str(e)}")
            return volume_multiplier * 10  # 返回基础分数
