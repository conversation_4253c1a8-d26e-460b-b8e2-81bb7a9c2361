"""
选股应用程序主入口
"""
import argparse
import logging
import sys
from datetime import datetime
from typing import Dict, List

from .data.access.data_access_factory import DataAccessFactory
from .strategies.strategy_manager import StrategyManager
from .utils.logger import setup_logger


class SelectionApp:
    """选股应用程序"""
    
    def __init__(self):
        self.logger = None
        self.data_access = None
        self.strategy_manager = None
    
    def initialize(self, db_type: str = 'mysql'):
        """初始化应用程序"""
        try:
            # 设置日志
            self.logger = setup_logger('selection_app', 'logs/selection_app.log')
            self.logger.info("初始化选股应用程序")
            
            # 创建数据访问对象
            self.data_access = DataAccessFactory.create_data_access(db_type)
            if not self.data_access:
                raise Exception(f"无法创建数据访问对象: {db_type}")
            
            # 创建策略管理器
            self.strategy_manager = StrategyManager(self.data_access)
            
            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"应用程序初始化失败: {str(e)}")
            else:
                print(f"应用程序初始化失败: {str(e)}")
            return False
    
    def run_selection(self, strategy_name: str = 'volume_anomaly', config: Dict = None, 
                     show_details: bool = False, save_results: bool = True) -> bool:
        """运行选股"""
        try:
            self.logger.info(f"开始运行选股策略: {strategy_name}")
            
            # 检查策略是否存在
            available_strategies = self.strategy_manager.get_available_strategies()
            if strategy_name not in available_strategies:
                self.logger.error(f"策略不存在: {strategy_name}")
                print(f"错误: 策略'{strategy_name}'不存在")
                print(f"可用策略: {', '.join(available_strategies)}")
                return False
            
            # 执行策略
            print(f"正在执行策略: {strategy_name}")
            print("请稍候...")
            
            results = self.strategy_manager.execute_strategy(
                strategy_name, config, save_results
            )
            
            # 显示结果
            if results:
                print("\n" + self.strategy_manager.format_results(results, show_details))
                
                # 显示统计信息
                self._show_statistics(results)
                
                if save_results:
                    print(f"\n选股结果已保存到数据库")
            else:
                print("\n没有找到符合条件的股票")
            
            return True
            
        except Exception as e:
            self.logger.error(f"运行选股失败: {str(e)}")
            print(f"运行选股失败: {str(e)}")
            return False
    
    def show_strategy_info(self, strategy_name: str = None):
        """显示策略信息"""
        try:
            if strategy_name:
                # 显示指定策略信息
                info = self.strategy_manager.get_strategy_info(strategy_name)
                if info:
                    print(f"\n策略名称: {info['name']}")
                    print(f"策略描述: {info['description']}")
                    print("\n默认配置:")
                    for key, value in info['config'].items():
                        print(f"  {key}: {value}")
                else:
                    print(f"策略不存在: {strategy_name}")
            else:
                # 显示所有策略
                strategies = self.strategy_manager.get_available_strategies()
                print("\n可用策略:")
                for strategy in strategies:
                    info = self.strategy_manager.get_strategy_info(strategy)
                    if info:
                        print(f"  {strategy}: {info['description']}")
        
        except Exception as e:
            self.logger.error(f"显示策略信息失败: {str(e)}")
            print(f"显示策略信息失败: {str(e)}")
    
    def show_history(self, strategy_name: str, date: str = None):
        """显示历史选股结果"""
        try:
            if date:
                selection_date = datetime.strptime(date, '%Y-%m-%d')
            else:
                selection_date = datetime.now()
            
            results = self.strategy_manager.get_strategy_results(strategy_name, selection_date)
            
            if results:
                print(f"\n{strategy_name} - {selection_date.strftime('%Y-%m-%d')} 选股结果:")
                print("=" * 60)
                
                for i, result in enumerate(results, 1):
                    print(f"{i:2d}. {result['stock_code']} - {result.get('stock_name', '')}")
                    print(f"    评分: {result['score']:.2f}")
                    print(f"    原因: {result['reason']}")
                    print()
            else:
                print(f"没有找到 {strategy_name} 在 {selection_date.strftime('%Y-%m-%d')} 的选股结果")
        
        except Exception as e:
            self.logger.error(f"显示历史结果失败: {str(e)}")
            print(f"显示历史结果失败: {str(e)}")
    
    def _show_statistics(self, results: List[Dict]):
        """显示统计信息"""
        if not results:
            return
        
        print("\n统计信息:")
        print("-" * 40)
        print(f"选中股票数量: {len(results)}")
        
        # 评分统计
        scores = [result['score'] for result in results]
        print(f"平均评分: {sum(scores) / len(scores):.2f}")
        print(f"最高评分: {max(scores):.2f}")
        print(f"最低评分: {min(scores):.2f}")
        
        # 交易量放大倍数统计（如果有的话）
        multipliers = [result.get('volume_multiplier') for result in results if result.get('volume_multiplier')]
        if multipliers:
            print(f"平均交易量放大倍数: {sum(multipliers) / len(multipliers):.2f}")
            print(f"最大交易量放大倍数: {max(multipliers):.2f}")
        
        # 价格分布
        prices = [result.get('close_price') for result in results if result.get('close_price')]
        if prices:
            print(f"价格范围: {min(prices):.2f} - {max(prices):.2f} 元")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.data_access:
                self.data_access.close_connection()
            self.logger.info("应用程序清理完成")
        except Exception as e:
            if self.logger:
                self.logger.error(f"清理资源失败: {str(e)}")


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description='A股智能选股系统')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 选股命令
    select_parser = subparsers.add_parser('select', help='执行选股')
    select_parser.add_argument('--strategy', '-s', default='volume_anomaly', 
                              help='策略名称 (默认: volume_anomaly)')
    select_parser.add_argument('--details', '-d', action='store_true', 
                              help='显示详细信息')
    select_parser.add_argument('--no-save', action='store_true', 
                              help='不保存结果到数据库')
    select_parser.add_argument('--db-type', default='mysql', choices=['mysql', 'sqlite'],
                              help='数据库类型 (默认: mysql)')
    
    # 策略信息命令
    info_parser = subparsers.add_parser('info', help='显示策略信息')
    info_parser.add_argument('strategy', nargs='?', help='策略名称 (可选)')
    
    # 历史结果命令
    history_parser = subparsers.add_parser('history', help='显示历史选股结果')
    history_parser.add_argument('strategy', help='策略名称')
    history_parser.add_argument('--date', '-d', help='日期 (格式: YYYY-MM-DD)')
    
    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    app = SelectionApp()
    
    try:
        # 初始化应用程序
        db_type = getattr(args, 'db_type', 'mysql')
        if not app.initialize(db_type):
            sys.exit(1)
        
        # 执行命令
        if args.command == 'select':
            success = app.run_selection(
                strategy_name=args.strategy,
                show_details=args.details,
                save_results=not args.no_save
            )
            if not success:
                sys.exit(1)
        
        elif args.command == 'info':
            app.show_strategy_info(args.strategy)
        
        elif args.command == 'history':
            app.show_history(args.strategy, args.date)
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行失败: {str(e)}")
        sys.exit(1)
    finally:
        app.cleanup()


if __name__ == '__main__':
    main()
