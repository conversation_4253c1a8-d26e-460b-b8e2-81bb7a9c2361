"""
增强的可视化策略验证器
"""
import logging
import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import pandas as pd

from .visual_validator import VisualValidator
from ..core.interfaces.visual_validator import (
    VisualValidationConfig, VisualValidationResult, SignalPoint
)
from ..core.interfaces.data_access import IDataAccess


class EnhancedVisualValidator(VisualValidator):
    """增强的可视化策略验证器"""

    def __init__(self, data_access: IDataAccess):
        super().__init__(data_access)
        self.validation_history = []
        self.logger = logging.getLogger(__name__)

    def batch_validate_stocks(self, 
                             stock_codes: List[str],
                             strategy_name: str,
                             start_date: datetime,
                             end_date: datetime,
                             save_charts: bool = True) -> List[VisualValidationResult]:
        """批量验证多只股票"""
        self.logger.info(f"开始批量验证 {len(stock_codes)} 只股票")
        
        results = []
        successful_count = 0
        failed_count = 0
        
        for i, stock_code in enumerate(stock_codes, 1):
            self.logger.info(f"验证进度: {i}/{len(stock_codes)} - {stock_code}")
            
            try:
                # 获取股票名称
                stock_info = self.data_access.get_stock_info(stock_code)
                stock_name = stock_info.get('stock_name', stock_code) if stock_info else stock_code
                
                # 配置验证参数
                config = VisualValidationConfig(
                    stock_code=stock_code,
                    strategy_name=strategy_name,
                    start_date=start_date,
                    end_date=end_date,
                    chart_title=f'{stock_name}({stock_code}) - {strategy_name}策略验证',
                    show_indicators=True,
                    show_volume=True,
                    save_path=f"charts/batch_{stock_code}_{strategy_name}_{start_date.strftime('%Y%m%d')}.png" if save_charts else None
                )
                
                # 执行验证
                result = self.validate_stock_visual(config)
                
                if result.error_message:
                    self.logger.warning(f"{stock_code} 验证失败: {result.error_message}")
                    failed_count += 1
                else:
                    self.logger.info(f"{stock_code} 验证成功，发现 {result.total_signals} 个信号")
                    successful_count += 1
                
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"{stock_code} 验证异常: {str(e)}")
                failed_count += 1
                
                # 创建错误结果
                error_result = VisualValidationResult(
                    config=VisualValidationConfig(
                        stock_code=stock_code,
                        strategy_name=strategy_name,
                        start_date=start_date,
                        end_date=end_date
                    ),
                    stock_name=stock_code,
                    signal_points=[],
                    error_message=str(e)
                )
                results.append(error_result)
        
        self.logger.info(f"批量验证完成: 成功 {successful_count}, 失败 {failed_count}")
        return results

    def generate_batch_report(self, results: List[VisualValidationResult]) -> str:
        """生成批量验证报告"""
        report = []
        report.append("=" * 100)
        report.append("批量可视化策略验证报告")
        report.append("=" * 100)
        
        # 统计信息
        total_stocks = len(results)
        successful_stocks = len([r for r in results if not r.error_message])
        failed_stocks = total_stocks - successful_stocks
        total_signals = sum(r.total_signals for r in results if not r.error_message)
        
        report.append(f"\n📊 验证统计:")
        report.append(f"总验证股票数: {total_stocks}")
        report.append(f"成功验证数: {successful_stocks}")
        report.append(f"失败验证数: {failed_stocks}")
        report.append(f"成功率: {successful_stocks/total_stocks*100:.1f}%")
        report.append(f"总信号数: {total_signals}")
        
        # 按信号数量排序
        successful_results = [r for r in results if not r.error_message]
        successful_results.sort(key=lambda x: x.total_signals, reverse=True)
        
        # 信号统计
        if successful_results:
            report.append(f"\n🎯 信号分布:")
            signal_counts = {}
            for result in successful_results:
                count = result.total_signals
                signal_counts[count] = signal_counts.get(count, 0) + 1
            
            for signal_count in sorted(signal_counts.keys(), reverse=True):
                stock_count = signal_counts[signal_count]
                report.append(f"  {signal_count}个信号: {stock_count}只股票")
        
        # 详细结果
        report.append(f"\n📋 详细验证结果:")
        report.append("-" * 100)
        report.append(f"{'序号':<4} {'股票代码':<10} {'股票名称':<15} {'信号数':<6} {'状态':<8} {'图表文件'}")
        report.append("-" * 100)
        
        for i, result in enumerate(results, 1):
            status = "成功" if not result.error_message else "失败"
            chart_file = os.path.basename(result.chart_path) if result.chart_path else "无"
            
            report.append(f"{i:<4} "
                         f"{result.config.stock_code:<10} "
                         f"{result.stock_name[:14]:<15} "
                         f"{result.total_signals:<6} "
                         f"{status:<8} "
                         f"{chart_file}")
            
            if result.error_message:
                report.append(f"     错误: {result.error_message}")
        
        # 最佳信号股票
        if successful_results:
            report.append(f"\n🏆 最佳信号股票 (前10名):")
            report.append("-" * 80)
            
            for i, result in enumerate(successful_results[:10], 1):
                if result.total_signals > 0:
                    report.append(f"{i}. {result.stock_name}({result.config.stock_code}): {result.total_signals}个信号")
                    
                    # 显示最高评分的信号
                    if result.signal_points:
                        best_signal = max(result.signal_points, key=lambda x: x.score)
                        report.append(f"   最佳信号: {best_signal.date.date()}, 评分: {best_signal.score:.1f}")
        
        return "\n".join(report)

    def save_validation_results(self, results: List[VisualValidationResult], filename: str = None) -> str:
        """保存验证结果到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"validation_results_{timestamp}.json"
        
        # 创建结果目录
        os.makedirs('results', exist_ok=True)
        filepath = os.path.join('results', filename)
        
        # 转换结果为可序列化的格式
        serializable_results = []
        for result in results:
            result_data = {
                'stock_code': result.config.stock_code,
                'stock_name': result.stock_name,
                'strategy_name': result.config.strategy_name,
                'start_date': result.config.start_date.isoformat(),
                'end_date': result.config.end_date.isoformat(),
                'total_signals': result.total_signals,
                'chart_path': result.chart_path,
                'error_message': result.error_message,
                'signal_points': []
            }
            
            # 添加信号点数据
            for signal in result.signal_points:
                signal_data = {
                    'date': signal.date.isoformat(),
                    'price': signal.price,
                    'score': signal.score,
                    'signal_type': signal.signal_type,
                    'indicators': signal.indicators,
                    'reason': signal.reason
                }
                result_data['signal_points'].append(signal_data)
            
            serializable_results.append(result_data)
        
        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_stocks': len(results),
                'successful_validations': len([r for r in results if not r.error_message]),
                'results': serializable_results
            }, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"验证结果已保存到: {filepath}")
        return filepath

    def compare_strategies(self, 
                          stock_codes: List[str],
                          strategy_names: List[str],
                          start_date: datetime,
                          end_date: datetime) -> Dict[str, List[VisualValidationResult]]:
        """比较多个策略的效果"""
        self.logger.info(f"开始比较 {len(strategy_names)} 个策略在 {len(stock_codes)} 只股票上的效果")
        
        strategy_results = {}
        
        for strategy_name in strategy_names:
            self.logger.info(f"验证策略: {strategy_name}")
            results = self.batch_validate_stocks(
                stock_codes, strategy_name, start_date, end_date, save_charts=False
            )
            strategy_results[strategy_name] = results
        
        return strategy_results

    def generate_strategy_comparison_report(self, 
                                          strategy_results: Dict[str, List[VisualValidationResult]]) -> str:
        """生成策略比较报告"""
        report = []
        report.append("=" * 100)
        report.append("策略效果比较报告")
        report.append("=" * 100)
        
        # 策略统计
        strategy_stats = {}
        for strategy_name, results in strategy_results.items():
            successful_results = [r for r in results if not r.error_message]
            total_signals = sum(r.total_signals for r in successful_results)
            avg_signals = total_signals / len(successful_results) if successful_results else 0
            
            strategy_stats[strategy_name] = {
                'total_stocks': len(results),
                'successful_stocks': len(successful_results),
                'total_signals': total_signals,
                'avg_signals': avg_signals,
                'success_rate': len(successful_results) / len(results) * 100 if results else 0
            }
        
        # 策略排名
        sorted_strategies = sorted(strategy_stats.items(), 
                                 key=lambda x: x[1]['total_signals'], reverse=True)
        
        report.append(f"\n🏆 策略效果排名:")
        report.append("-" * 80)
        report.append(f"{'排名':<4} {'策略名称':<20} {'总信号':<8} {'平均信号':<10} {'成功率':<8}")
        report.append("-" * 80)
        
        for i, (strategy_name, stats) in enumerate(sorted_strategies, 1):
            report.append(f"{i:<4} "
                         f"{strategy_name:<20} "
                         f"{stats['total_signals']:<8} "
                         f"{stats['avg_signals']:<10.2f} "
                         f"{stats['success_rate']:<7.1f}%")
        
        return "\n".join(report)
