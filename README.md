# A股智能选股系统

基于Python的A股智能选股系统，采用分层架构设计，支持多数据源和多策略扩展。

## 项目状态

当前版本：**v0.1** - 基础框架 + 数据获取

**注意**：如果运行测试脚本时出现"没有接口访问权限"错误，请检查你的Tushare账号是否具有相应接口的权限。部分接口需要一定的积分才能访问，具体权限要求请参考[Tushare积分说明](https://tushare.pro/document/1?doc_id=108)。

## 功能特性

### v0.1 已实现功能
- ✅ 分层架构设计（接口抽象 + 具体实现）
- ✅ Tushare数据源集成（已从akshare迁移）
- ✅ MySQL数据存储（主要）+ SQLite（备用）
- ✅ 股票基本信息管理
- ✅ 日K线数据获取和存储
- ✅ 完整的异常处理机制
- ✅ 日志系统
- ✅ 单元测试和集成测试

### 计划功能（后续版本）
- 🔄 v0.2: 交易量异动选股策略
- 🔄 v0.3: 定时任务 + 邮件通知
- 🔄 v0.4: 配置管理 + 性能优化
- 🔄 v0.5: 完善功能 + 部署支持

## 项目结构

```
select-in-ai/
├── README.md                   # 项目说明
├── requirements.txt            # 依赖包
├── src/                       # 源代码
│   ├── core/                  # 核心模块
│   │   ├── interfaces/        # 抽象接口
│   │   ├── models/           # 数据模型
│   │   └── exceptions/       # 自定义异常
│   ├── data/                 # 数据层
│   │   ├── sources/          # 数据源实现
│   │   └── access/           # 数据访问实现
│   ├── strategies/           # 选股策略（v0.2+）
│   ├── scheduler/            # 定时任务（v0.3+）
│   ├── notification/         # 通知模块（v0.3+）
│   ├── config/              # 配置管理（v0.4+）
│   ├── utils/               # 工具模块
│   └── main.py              # 主程序入口
├── tests/                   # 测试代码
├── scripts/                 # 脚本工具
├── config/                  # 配置文件
├── data/                    # 数据文件
├── logs/                    # 日志文件
└── doc/                     # 文档
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd select-in-ai

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. Tushare配置

本系统使用Tushare作为数据源，需要配置API Token：

```bash
# 1. 访问 https://tushare.pro/ 注册账号并获取Token
# 2. 运行配置脚本
python scripts/setup_tushare_token.py

# 3. 验证配置
python scripts/test_tushare_basic.py
```

详细配置说明请参考：[Tushare数据源配置指南](doc/Tushare数据源配置指南.md)

### 3. 数据库配置

#### MySQL配置（推荐）
```bash
# 1. 确保MySQL服务器运行在localhost:3306
# 2. 复制环境变量配置文件
cp .env.example .env

# 3. 编辑.env文件，设置MySQL连接参数
# DATABASE_TYPE=mysql
# MYSQL_HOST=localhost
# MYSQL_PORT=3306
# MYSQL_USER=root
# MYSQL_PASSWORD=your_password
# MYSQL_DATABASE=stock_selection
```

#### SQLite配置（备用）
```bash
# 在.env文件中设置
# DATABASE_TYPE=sqlite
# SQLITE_DB_PATH=data/stock_selection.db
```

### 4. 运行测试

```bash
# 测试Tushare数据源基本功能（不需要Token）
python scripts/test_tushare_basic.py

# 测试Tushare数据源完整功能（需要Token）
python scripts/test_tushare_source.py

# 运行MySQL数据库测试
python scripts/test_mysql.py

# 测试新的数据更新功能
python scripts/test_data_update.py
```

### 5. 使用主程序

#### 基础功能
```bash
# 更新股票列表
python src/main.py --update-stocks

# 更新最近30天交易数据（限制前10只股票用于测试）
python src/main.py --update-data --days 30 --limit 10

# 显示统计信息
python src/main.py --stats

# 查看帮助
python src/main.py --help
```

#### 增量数据更新
```bash
# 传统增量更新：自动检测需要更新的股票并获取缺失数据
python src/main.py --incremental

# 优化增量更新（推荐）：专注T-1交易日数据，智能缓存，批量处理
python src/main.py --incremental-optimized
```

#### 历史数据补充
```bash
# 传统历史数据补充
python src/main.py --historical

# 优化历史数据补充（推荐）：批量处理，智能缓存，更好的性能
python src/main.py --historical-optimized

# 补充指定时间范围的历史数据
python src/main.py --historical-optimized --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical-optimized --stocks 000001,000002,600000 --start-date 2024-01-01

# 补充指定股票指定时间范围的历史数据
python src/main.py --historical-optimized --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

#### 功能组合使用
```bash
# 先更新股票列表，再进行优化增量数据更新（推荐）
python src/main.py --update-stocks --incremental-optimized

# 更新股票列表后补充历史数据（推荐）
python src/main.py --update-stocks --historical-optimized --start-date 2024-01-01
```

#### 优化功能特性
- **智能缓存**: 股票列表和交易日历缓存，减少重复请求
- **批量处理**: 分批处理股票，提高处理效率
- **随机延时**: 0.3-0.8秒随机延时，避免规律性请求
- **专注T-1数据**: 移除实时数据获取，专注历史交易数据
- **错误隔离**: 单只股票失败不影响整批处理
- **详细监控**: 提供详细的处理进度和统计信息

详细说明请参考：[优化数据获取功能说明](doc/优化数据获取功能说明.md)

## 架构设计

### 核心设计原则
- **接口抽象**: 数据源、存储、策略都通过接口隔离
- **依赖隔离**: 各模块独立，便于测试和维护
- **可扩展性**: 支持新增数据源、存储方案和选股策略

### 主要接口
- `IDataSource`: 数据源抽象接口
- `IDataAccess`: 数据访问抽象接口
- `ISelectionStrategy`: 选股策略抽象接口（v0.2+）
- `INotifier`: 通知抽象接口（v0.3+）

## 开发计划

详细的开发计划请参考：[开发计划文档](doc/开发计划.md)

### 版本里程碑
- [x] **v0.1**: 基础框架 + 数据获取 ✅
- [ ] **v0.2**: 简单选股策略
- [ ] **v0.3**: 定时任务 + 通知
- [ ] **v0.4**: 配置管理 + 优化
- [ ] **v0.5**: 完善功能 + 部署

## 测试

### 运行测试
```bash
# v0.1版本功能测试
python scripts/test_v01.py

# 优化数据获取功能测试
python tests/test_optimized_data_service.py

# 单元测试（后续版本）
pytest tests/unit/

# 集成测试（后续版本）
pytest tests/integration/
```

### 测试覆盖
- 数据源功能测试
- 数据访问功能测试
- 集成测试
- 异常处理测试

## 文档

- [产品需求文档 (PRD)](doc/PRD_A股选股系统.md)
- [架构设计文档](doc/架构设计文档.md)
- [开发计划](doc/开发计划.md)

## 技术栈

- **Python 3.8+**
- **数据获取**: Tushare (主要), akshare (备用)
- **数据库**: MySQL (主要), SQLite (备用)
- **数据处理**: pandas, numpy
- **测试**: pytest
- **代码质量**: flake8, black, mypy

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue。
