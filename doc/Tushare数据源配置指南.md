# Tushare数据源配置指南

## 概述

本系统已从akshare数据源迁移到tushare数据源。Tushare是一个专业的金融数据接口，提供更稳定和丰富的A股数据服务。

## 配置步骤

### 1. 获取Tushare Token

1. 访问 [Tushare官网](https://tushare.pro/)
2. 注册账号并登录
3. 在个人中心获取API Token
4. 记录下您的Token（格式类似：`xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`）

### 2. 配置Token

#### 方法一：使用配置脚本（推荐）

```bash
# 运行配置脚本
python scripts/setup_tushare_token.py

# 或者快速设置
python scripts/setup_tushare_token.py your_token_here
```

#### 方法二：手动配置文件

```bash
# 1. 复制配置模板
cp config/tushare_token.json.example config/tushare_token.json

# 2. 编辑配置文件，将your_tushare_token_here替换为您的真实Token
# config/tushare_token.json:
{
  "token": "your_real_token_here"
}
```

#### 方法三：环境变量（备用）

```bash
# 设置环境变量（系统会自动检测）
export TUSHARE_TOKEN="your_token_here"
```

### 3. 验证配置

运行测试脚本验证配置是否正确：

```bash
# 激活虚拟环境
source venv/bin/activate

# 基本功能测试（无需Token）
python scripts/test_tushare_basic.py

# 完整功能测试（需要Token）
python scripts/test_tushare_source.py
```

## 数据源对比

| 特性 | Akshare | Tushare |
|------|---------|---------|
| 数据稳定性 | 中等 | 高 |
| API限制 | 较少 | 有限制但合理 |
| 数据质量 | 良好 | 优秀 |
| 更新频率 | 实时 | 实时 |
| 历史数据 | 丰富 | 非常丰富 |
| 技术指标 | 基础 | 丰富 |

## 主要变化

### 1. 数据格式变化

- **股票代码格式**:
  - 内部统一使用6位数字格式（如：000001）
  - Tushare API使用带后缀格式（如：000001.SZ）
  - 系统自动进行格式转换

### 2. 新增功能

- **交易日历**: 准确的交易日历数据
- **更丰富的股票信息**: 包含行业分类、上市日期等
- **更稳定的数据获取**: 内置重试机制和频率控制

### 3. 配置参数

可在 `src/config/tushare_config.py` 中调整以下参数：

```python
# API配置
api_timeout = 30              # API超时时间（秒）
retry_times = 3               # 重试次数
retry_delay = 1               # 重试延时（秒）

# 请求频率控制
request_delay_range = (0.1, 0.3)    # 随机延时范围（秒）
max_requests_per_minute = 200       # 每分钟最大请求数

# 数据获取配置
batch_size = 50               # 批量处理大小
max_workers = 3               # 并发线程数
```

## 使用说明

### 1. 基本数据获取

系统的基本使用方式保持不变：

```bash
# 更新股票列表
python src/main.py --update-stocks

# 更新交易数据
python src/main.py --update-data --days 30

# 增量更新
python src/main.py --incremental
```

### 2. 历史数据补充

```bash
# 补充指定时间段的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31
```

## 故障排除

### 1. Token相关问题

**问题**: `Tushare token未配置`
**解决**: 确保正确设置了环境变量 `TUSHARE_TOKEN`

**问题**: `Token无效`
**解决**:
- 检查Token是否正确复制
- 确认Tushare账号状态正常
- 检查API调用次数是否超限

### 2. 网络相关问题

**问题**: `连接超时`
**解决**:
- 检查网络连接
- 增加 `api_timeout` 配置值
- 检查防火墙设置

### 3. 频率限制问题

**问题**: `请求频率过高`
**解决**:
- 增加 `request_delay_range` 的延时
- 减少 `max_workers` 并发数
- 检查 `max_requests_per_minute` 设置

## 注意事项

1. **API限制**: Tushare对免费用户有API调用次数限制，请合理使用
2. **数据延时**: 部分数据可能有轻微延时，这是正常现象
3. **Token安全**: 请妥善保管您的Token，不要在代码中硬编码
4. **备份数据**: 建议定期备份重要的历史数据

## 技术支持

如果遇到问题，请：

1. 首先运行测试脚本诊断问题
2. 检查日志文件 `logs/app.log`
3. 参考Tushare官方文档
4. 联系技术支持

## 更新日志

- **2024-01-XX**: 完成从akshare到tushare的数据源迁移
- 保持了原有的接口兼容性
- 新增了更丰富的数据获取功能
- 优化了错误处理和重试机制
