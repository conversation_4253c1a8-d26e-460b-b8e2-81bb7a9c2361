# 数据获取功能说明

## 概述

A股智能选股系统的数据获取功能已经增强，现在支持两种主要的数据获取模式：

1. **增量获取**：智能检测需要更新的数据，适用于每日定时任务
2. **历史数据补充**：指定时间范围和股票范围的历史数据获取，适用于数据补充和初始化

## 功能特性

### 🔄 增量获取功能

#### 核心特性
- **智能检测**：自动识别需要更新数据的股票
- **增量更新**：只获取缺失的交易日数据
- **效率优化**：避免重复获取已存在的数据
- **交易日历**：基于真实交易日历，排除节假日和周末

#### 使用场景
- 每日定时任务运行
- 系统维护后的数据同步
- 确保数据的实时性

#### 命令示例
```bash
# 执行增量数据更新
python src/main.py --incremental
```

#### 工作流程
1. 检查所有股票的最新数据日期
2. 与当前日期比较，识别需要更新的股票
3. 对每只需要更新的股票，获取缺失日期的数据
4. 批量保存到数据库

### 📊 历史数据补充功能

#### 核心特性
- **时间范围指定**：支持自定义开始和结束日期
- **股票范围指定**：可指定特定股票或获取所有股票
- **缺失检测**：智能检测指定范围内的缺失数据
- **批量处理**：高效处理大量历史数据

#### 使用场景
- 系统初始化时获取历史数据
- 补充特定时间段的缺失数据
- 为新增股票获取历史数据
- 数据质量检查和修复

#### 命令示例
```bash
# 补充所有股票最近90天的历史数据（默认）
python src/main.py --historical

# 补充指定时间范围的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical --stocks 000001,000002,600000 --start-date 2024-01-01

# 补充指定股票指定时间范围的历史数据
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

#### 参数说明
- `--start-date`：开始日期，格式为 YYYY-MM-DD，默认为90天前
- `--end-date`：结束日期，格式为 YYYY-MM-DD，默认为当前日期
- `--stocks`：股票代码列表，用逗号分隔，默认为所有股票

## 技术实现

### 新增接口方法

#### 数据源接口 (IDataSource)
```python
def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
    """获取交易日历（排除节假日和周末）"""
```

#### 数据访问接口 (IDataAccess)
```python
def get_missing_dates(self, stock_code: str, start_date: datetime, end_date: datetime) -> List[datetime]:
    """获取指定股票在指定时间范围内缺失的交易日期"""

def get_stocks_need_update(self, target_date: datetime = None) -> List[str]:
    """获取需要更新数据的股票列表"""
```

### 核心算法

#### 增量检测算法
1. 获取所有股票的最新交易日期
2. 与目标日期比较（默认为当前日期）
3. 考虑周末和节假日，判断是否真正需要更新
4. 返回需要更新的股票列表

#### 缺失日期检测算法
1. 生成指定时间范围内的所有工作日
2. 查询数据库中已存在的交易日期
3. 计算差集，得到缺失的日期列表
4. 返回需要补充的日期

### 性能优化

#### 数据获取优化
- **批量获取**：一次性获取整个时间段的数据，而不是逐日获取
- **请求限制**：在股票间添加延时，避免API请求过于频繁
- **重试机制**：网络异常时自动重试

#### 数据库优化
- **批量插入**：使用批量插入提高数据保存效率
- **重复处理**：使用 ON DUPLICATE KEY UPDATE 处理重复数据
- **连接管理**：使用连接池和上下文管理器

## 使用建议

### 日常运维
1. **每日增量更新**：建议在交易日结束后运行增量更新
2. **定期数据检查**：定期运行历史数据补充检查数据完整性
3. **监控日志**：关注日志输出，及时发现和处理异常

### 初始化部署
1. **股票列表**：首先更新股票列表
2. **历史数据**：根据需要获取历史数据（建议从最近3个月开始）
3. **数据验证**：运行测试脚本验证数据完整性

### 故障恢复
1. **网络异常**：重新运行相应的更新命令
2. **数据缺失**：使用历史数据补充功能修复
3. **数据库异常**：检查数据库连接和权限

## 命令行参考

### 完整参数列表
```bash
python src/main.py [选项]

选项:
  --update-stocks     更新股票列表
  --update-data       更新交易数据（原有功能）
  --incremental       增量更新数据
  --historical        历史数据补充
  --days N           更新最近N天的数据（默认30天）
  --start-date DATE   开始日期（YYYY-MM-DD格式）
  --end-date DATE     结束日期（YYYY-MM-DD格式）
  --stocks CODES      股票代码列表（逗号分隔）
  --limit N          限制处理的股票数量（测试用）
  --stats            显示统计信息
  --help             显示帮助信息
```

### 常用组合
```bash
# 完整的数据初始化
python src/main.py --update-stocks --historical --start-date 2024-01-01

# 日常维护
python src/main.py --incremental --stats

# 数据修复
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

## 测试验证

### 功能测试
运行测试脚本验证功能：
```bash
python scripts/test_data_update.py
```

测试内容包括：
- 交易日历功能测试
- 缺失日期检测测试
- 需要更新股票检测测试
- 增量更新逻辑测试

### 数据验证
```bash
# 查看系统统计信息
python src/main.py --stats

# 检查特定股票的数据完整性
python src/main.py --historical --stocks 000001 --start-date 2024-01-01 --end-date 2024-01-31
```

## 注意事项

1. **API限制**：akshare有请求频率限制，大量数据获取时请耐心等待
2. **网络稳定性**：确保网络连接稳定，避免数据获取中断
3. **数据库权限**：确保数据库用户有足够的读写权限
4. **磁盘空间**：历史数据较大，确保有足够的存储空间
5. **时间设置**：注意服务器时间设置，影响增量更新的判断

## 版本信息

- **功能版本**：v0.1.1
- **更新日期**：2024年12月
- **兼容性**：向后兼容v0.1版本的所有功能
