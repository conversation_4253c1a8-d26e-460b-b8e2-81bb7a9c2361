# A股智能选股系统 v0.2 版本使用说明

## 版本概述

v0.2版本实现了核心的选股策略功能，能够根据交易量异动规则筛选股票。

### 🎯 主要功能

1. **交易量异动选股策略** - 基于交易量突然放大筛选股票
2. **策略管理框架** - 支持多策略扩展的架构
3. **命令行界面** - 便捷的选股操作和结果查看
4. **结果存储** - 选股结果保存到数据库
5. **历史查询** - 查看历史选股结果

## 安装和环境

### 前置条件
- Python 3.8+
- MySQL数据库（已配置）
- 虚拟环境已激活

### 激活环境
```bash
cd /path/to/select-in-ai
source venv/bin/activate
```

## 使用方法

### 1. 查看可用策略

```bash
python run_selection.py info
```

查看特定策略详情：
```bash
python run_selection.py info volume_anomaly
```

### 2. 执行选股

#### 基本选股（使用默认配置）
```bash
python run_selection.py select
```

#### 显示详细信息
```bash
python run_selection.py select --details
```

#### 不保存结果（仅查看）
```bash
python run_selection.py select --no-save
```

#### 指定数据库类型
```bash
python run_selection.py select --db-type mysql
python run_selection.py select --db-type sqlite
```

### 3. 查看历史结果

```bash
python run_selection.py history "交易量异动策略"
```

指定日期查看：
```bash
python run_selection.py history "交易量异动策略" --date 2025-05-27
```

## 策略配置

### 交易量异动策略参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| volume_multiplier_threshold | 2.0 | 交易量放大倍数阈值 |
| min_price | 3.0 | 最低股价（元） |
| max_price | 100.0 | 最高股价（元） |
| baseline_days | 14 | 基准期天数（2周） |
| exclude_st | True | 是否排除ST股票 |
| min_volume | 1000000 | 最小交易量（手） |
| max_results | 20 | 最大结果数量 |

### 自定义配置示例

创建Python脚本使用自定义配置：

```python
from src.selection_app import SelectionApp

app = SelectionApp()
app.initialize('mysql')

# 自定义配置
custom_config = {
    'volume_multiplier_threshold': 1.5,  # 降低阈值
    'min_price': 5.0,
    'max_price': 50.0,
    'baseline_days': 10,
    'max_results': 10
}

app.run_selection(
    strategy_name='volume_anomaly',
    config=custom_config,
    show_details=True,
    save_results=True
)

app.cleanup()
```

## 输出说明

### 选股结果格式

```
================================================================================
选股结果 (共5只股票)
================================================================================
 1. 600192 - 长城电工
    评分: 123.40
    原因: 交易量放大9.34倍，当前价格8.09元
    交易量放大: 9.34倍
    收盘价: 8.09元
    最新交易量: 516,572手

统计信息:
----------------------------------------
选中股票数量: 5
平均评分: 97.84
最高评分: 123.40
最低评分: 78.98
平均交易量放大倍数: 7.33
最大交易量放大倍数: 9.34
价格范围: 5.04 - 19.41 元
```

### 评分机制

选股评分由以下因素组成：
- **基础分数**：交易量放大倍数 × 10（最高100分）
- **价格位置加分**：收盘价在当日高低价中的位置（最高10分）
- **换手率加分**：换手率 × 2（最高20分）

## 测试功能

### 运行完整测试
```bash
python scripts/test_v02.py
```

### 测试自定义配置选股
```bash
python test_selection_with_config.py
```

### 检查数据库结果
```bash
python check_results.py
```

## 常见问题

### Q: 没有找到符合条件的股票
**A:** 可能是阈值设置过高，可以：
- 降低 `volume_multiplier_threshold` 参数
- 减少 `baseline_days` 参数
- 降低 `min_volume` 参数

### Q: 历史查询没有结果
**A:** 确保使用正确的策略名称：
- 使用中文名称："交易量异动策略"
- 而不是英文名称："volume_anomaly"

### Q: 数据库连接失败
**A:** 检查MySQL服务是否运行：
```bash
docker ps  # 查看MySQL容器状态
```

## 日志文件

- 应用程序日志：`logs/selection_app.log`
- 测试日志：`logs/test_v02.log`

## 下一步计划

v0.3版本将实现：
- 定时任务自动执行
- 邮件通知功能
- 完整的自动化流程

---

**版本**：v0.2  
**更新日期**：2025年5月27日  
**状态**：✅ 功能完整，测试通过
