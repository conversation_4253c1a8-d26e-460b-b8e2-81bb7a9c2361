# 可视化策略验证功能说明

## 功能概述

可视化策略验证功能是A股选股系统的新增功能，能够对特定股票的历史数据进行策略验证，并在K线图中标注出符合策略要求的信号点，提供直观的可视化验证结果。

## 主要特性

### 1. 可视化验证
- 对单只股票进行策略验证
- 在K线图中标注策略信号点
- 显示技术指标（布林带、RSI等）
- 展示成交量变化

### 2. 图表功能
- 绘制专业的K线图
- 显示布林带上下轨和中轨
- RSI指标图表
- 成交量柱状图
- 信号点标注和评分显示

### 3. 验证报告
- 生成详细的验证总结
- 列出所有信号点的详细信息
- 包含策略评分和技术指标值

## 核心组件

### 1. VisualValidator类
主要的可视化验证器，提供以下功能：
- `validate_stock_visual()`: 执行可视化验证
- `find_strategy_signals()`: 查找策略信号点
- `generate_chart()`: 生成K线图表

### 2. 配置类
- `VisualValidationConfig`: 验证配置
- `SignalPoint`: 信号点数据结构
- `VisualValidationResult`: 验证结果

## 使用方法

### 1. 基本使用

```python
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig
from src.data.access.mysql_data_access import MySQLDataAccess
from datetime import datetime

# 创建数据访问和验证器
data_access = MySQLDataAccess()
visual_validator = VisualValidator(data_access)

# 配置验证参数
config = VisualValidationConfig(
    stock_code='601111',  # 股票代码
    strategy_name='technical_reversal',  # 策略名称
    start_date=datetime(2024, 3, 15),  # 开始日期
    end_date=datetime(2024, 5, 31),    # 结束日期
    chart_title='股票策略验证',
    show_indicators=True,  # 显示技术指标
    show_volume=True,      # 显示成交量
    figure_size=(16, 12)   # 图表大小
)

# 执行验证
result = visual_validator.validate_stock_visual(config)

# 查看结果
if result.error_message:
    print(f"验证失败: {result.error_message}")
else:
    print(f"发现 {result.total_signals} 个信号点")
    print(f"图表保存到: {result.chart_path}")
    print(result.validation_summary)
```

### 2. 运行示例脚本

```bash
# 激活虚拟环境
source venv/bin/activate

# 单股票验证
python scripts/visual_validation_example.py

# 多股票批量验证
python scripts/visual_validation_example.py multiple
```

## 配置参数说明

### VisualValidationConfig参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| stock_code | str | 是 | 股票代码 |
| strategy_name | str | 是 | 策略名称 |
| start_date | datetime | 是 | 验证开始日期 |
| end_date | datetime | 是 | 验证结束日期 |
| chart_title | str | 否 | 图表标题 |
| save_path | str | 否 | 图表保存路径 |
| show_indicators | bool | 否 | 是否显示技术指标（默认True） |
| show_volume | bool | 否 | 是否显示成交量（默认True） |
| figure_size | tuple | 否 | 图表大小（默认(15,10)） |

## 输出结果

### 1. 图表文件
- 保存为PNG格式的高清图表
- 默认保存在`charts/`目录下
- 文件名格式：`{股票代码}_{策略名称}_{时间戳}.png`

### 2. 验证结果
包含以下信息：
- 股票基本信息
- 信号点列表
- 验证总结报告
- 图表保存路径

### 3. 信号点详情
每个信号点包含：
- 信号日期
- 信号价格
- 策略评分
- 技术指标值（RSI、量比等）
- 信号原因

## 图表说明

### 1. 主图（K线图）
- 红色K线：收盘价高于开盘价
- 绿色K线：收盘价低于开盘价
- 蓝色区域：布林带范围
- 红色箭头：买入信号点
- 评分标注：信号点的策略评分

### 2. 成交量图
- 红色柱：上涨日成交量
- 绿色柱：下跌日成交量
- 蓝色线：成交量移动平均线
- 红色虚线：信号日期标记

### 3. RSI指标图
- 紫色线：RSI指标线
- 红色虚线：超买线（70）
- 绿色虚线：超卖线（30）
- 红色圆点：信号日期的RSI值

## 注意事项

### 1. 数据要求
- 需要足够的历史数据（至少30天）
- 确保数据库中有相关股票的交易数据
- 验证期间应在数据可用范围内

### 2. 性能考虑
- 验证时间范围不宜过长（建议3个月内）
- 大量股票批量验证时注意内存使用
- 图表生成需要一定时间

### 3. 依赖包
确保安装了以下依赖包：
- matplotlib>=3.7.0
- mplfinance>=0.12.0
- pandas>=2.0.0
- numpy>=1.24.0

## 故障排除

### 1. 图表显示问题
如果图表中文显示异常，检查系统字体配置：
```python
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
```

### 2. 数据获取失败
- 检查数据库连接
- 确认股票代码正确
- 验证日期范围内有交易数据

### 3. 策略信号为空
- 检查策略配置是否正确
- 确认策略条件不会过于严格
- 验证历史数据的完整性

## 快速开始

### 运行演示脚本

```bash
# 激活虚拟环境
source venv/bin/activate

# 运行完整演示
python scripts/demo_visual_validation.py

# 运行基础测试
python scripts/test_visual_complete.py
```

### 演示结果
演示脚本会：
1. 对中国国航(601111)进行策略验证
2. 生成包含信号标注的K线图
3. 输出详细的验证报告
4. 展示多时间段验证功能

## 实际应用案例

### 案例1: 单股票历史验证
通过对中国国航2024年3-5月的数据验证，发现了3个技术反转信号点：
- 2024-04-12: 评分103.0，RSI 34.6，量比1.69倍
- 2024-04-15: 评分84.1，RSI 43.8，量比1.05倍
- 2024-04-19: 评分84.3，RSI 43.5，量比1.50倍

### 案例2: 策略效果可视化
生成的K线图清晰显示：
- 布林带上下轨道
- RSI超卖区域
- 成交量异常放大
- 策略信号标注点

## 扩展功能

### 1. 自定义图表样式
可以通过修改配置参数自定义图表外观：
- 调整图表大小
- 修改颜色方案
- 添加更多技术指标

### 2. 批量验证
支持对多只股票进行批量可视化验证，便于策略效果对比。

### 3. 导出功能
验证结果可以导出为多种格式，便于后续分析和报告。

## 版本更新

### v1.0 (2024-05-31)
- ✅ 基础可视化验证功能
- ✅ K线图表生成
- ✅ 技术指标显示
- ✅ 信号点标注
- ✅ 验证报告生成
- ✅ 多时间段验证
- ✅ 完整测试覆盖
