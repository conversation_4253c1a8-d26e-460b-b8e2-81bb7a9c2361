# Token配置文件管理说明

## 概述

系统已更新为使用配置文件管理Tushare Token，而不是依赖环境变量。这种方式更加便于管理和使用。

## 配置方式

### 方法一：使用配置脚本（推荐）

#### 交互式配置
```bash
python scripts/setup_tushare_token.py
```

功能特性：
- 🔧 交互式菜单操作
- 🔍 Token格式验证
- 🌐 连接测试
- 💾 自动保存到配置文件
- 🗑️ 配置管理（查看、删除）

#### 快速配置
```bash
python scripts/setup_tushare_token.py your_token_here
```

### 方法二：手动配置文件

```bash
# 1. 复制模板
cp config/tushare_token.json.example config/tushare_token.json

# 2. 编辑配置文件
{
  "token": "your_real_token_here"
}
```

### 方法三：环境变量（备用）

系统仍支持环境变量作为备用方案：
```bash
export TUSHARE_TOKEN="your_token_here"
```

## 配置文件结构

### 配置文件位置
```
config/tushare_token.json
```

### 配置文件格式
```json
{
  "token": "your_tushare_token_here"
}
```

### 模板文件
```
config/tushare_token.json.example
```

## 配置优先级

系统按以下优先级加载Token：

1. **配置文件** - `config/tushare_token.json`
2. **环境变量** - `TUSHARE_TOKEN`

## 安全性

### 文件保护
- 配置文件已添加到`.gitignore`，不会被提交到版本控制
- 只有模板文件会被提交，不包含真实Token

### Token保护
- 配置脚本中Token显示为掩码格式
- 支持隐藏输入模式
- 自动验证Token格式

## 使用说明

### 1. 首次配置

```bash
# 运行配置脚本
python scripts/setup_tushare_token.py

# 按提示输入Token
# 系统会自动验证和保存
```

### 2. 验证配置

```bash
# 基本功能测试
python scripts/test_tushare_basic.py

# 完整功能测试（需要有效Token）
python scripts/test_tushare_source.py
```

### 3. 管理配置

```bash
# 运行配置脚本进入管理菜单
python scripts/setup_tushare_token.py

# 选择相应操作：
# 1. 设置新的Token
# 2. 查看当前配置  
# 3. 测试Token连接
# 4. 删除Token配置
# 5. 退出
```

## 配置脚本功能

### 主要功能

1. **Token设置**
   - 交互式输入
   - 格式验证
   - 连接测试
   - 自动保存

2. **配置管理**
   - 查看当前状态
   - 测试连接
   - 删除配置

3. **安全特性**
   - Token掩码显示
   - 隐藏输入选项
   - 格式验证

### 使用示例

```bash
# 交互式配置
$ python scripts/setup_tushare_token.py

============================================================
🔧 Tushare Token 配置工具
============================================================
此工具将帮助您配置Tushare API Token
Token将安全保存在本地配置文件中

📋 当前配置状态:
----------------------------------------
Token状态: ❌ 未配置
配置文件: /path/to/config/tushare_token.json
文件存在: ❌ 否

🔧 请选择操作:
1. 设置新的Token
2. 查看当前配置
3. 测试Token连接
4. 删除Token配置
5. 退出

请输入选项 (1-5): 1
```

## 故障排除

### 常见问题

1. **配置文件不存在**
   ```bash
   # 运行配置脚本创建
   python scripts/setup_tushare_token.py
   ```

2. **Token格式错误**
   ```bash
   # 检查Token长度和格式
   # Token应为30+字符的字母数字组合
   ```

3. **权限问题**
   ```bash
   # 确保有写入config目录的权限
   chmod 755 config/
   ```

### 调试方法

1. **检查配置状态**
   ```bash
   python scripts/test_tushare_basic.py
   ```

2. **查看配置文件**
   ```bash
   cat config/tushare_token.json
   ```

3. **测试连接**
   ```bash
   python scripts/setup_tushare_token.py
   # 选择选项3进行连接测试
   ```

## 迁移说明

### 从环境变量迁移

如果您之前使用环境变量：

1. 系统会自动检测环境变量中的Token
2. 运行配置脚本将Token保存到配置文件
3. 之后可以移除环境变量设置

### 配置文件格式升级

当前配置文件格式简单明了，未来如需扩展：

```json
{
  "token": "your_token",
  "api_config": {
    "timeout": 30,
    "retry_times": 3
  }
}
```

## 最佳实践

1. **定期检查Token有效性**
   ```bash
   python scripts/setup_tushare_token.py
   # 选择选项3测试连接
   ```

2. **备份配置**
   ```bash
   # 备份配置文件（注意安全）
   cp config/tushare_token.json config/tushare_token.json.backup
   ```

3. **团队协作**
   - 每个开发者使用自己的Token
   - 不要共享Token配置文件
   - 使用模板文件指导配置

## 总结

新的配置文件管理方式提供了：

- ✅ 更便捷的Token管理
- ✅ 更好的安全性
- ✅ 更友好的用户体验
- ✅ 向后兼容性
- ✅ 完整的管理工具

这种方式既保持了系统的易用性，又提供了更好的Token管理体验。
