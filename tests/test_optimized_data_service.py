#!/usr/bin/env python3
"""
优化数据服务测试脚本
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.akshare_source import AkshareDataSource
from src.data.access.data_access_factory import DataAccessFactory
from src.data.services.optimized_data_service import OptimizedDataService
from src.utils.logger import setup_logger


def test_akshare_source_optimizations():
    """测试akshare数据源优化功能"""
    print("=" * 60)
    print("测试 AkshareDataSource 优化功能")
    print("=" * 60)
    
    # 创建数据源
    data_source = AkshareDataSource()
    
    # 测试缓存功能
    print("\n1. 测试股票列表缓存功能")
    print("首次获取股票列表...")
    start_time = datetime.now()
    stock_list1 = data_source.get_stock_list(use_cache=False)
    time1 = (datetime.now() - start_time).total_seconds()
    print(f"首次获取耗时: {time1:.2f}秒，股票数量: {len(stock_list1)}")
    
    print("使用缓存获取股票列表...")
    start_time = datetime.now()
    stock_list2 = data_source.get_stock_list(use_cache=True)
    time2 = (datetime.now() - start_time).total_seconds()
    print(f"缓存获取耗时: {time2:.2f}秒，股票数量: {len(stock_list2)}")
    print(f"缓存加速比: {time1/time2:.1f}x")
    
    # 测试交易日历缓存
    print("\n2. 测试交易日历缓存功能")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print("首次获取交易日历...")
    start_time = datetime.now()
    calendar1 = data_source.get_trading_calendar(start_date, end_date)
    time1 = (datetime.now() - start_time).total_seconds()
    print(f"首次获取耗时: {time1:.2f}秒，交易日数量: {len(calendar1)}")
    
    print("使用缓存获取交易日历...")
    start_time = datetime.now()
    calendar2 = data_source.get_trading_calendar(start_date, end_date)
    time2 = (datetime.now() - start_time).total_seconds()
    print(f"缓存获取耗时: {time2:.2f}秒，交易日数量: {len(calendar2)}")
    print(f"缓存加速比: {time1/time2:.1f}x")
    
    # 测试批量获取功能
    print("\n3. 测试批量数据获取功能")
    test_stocks = ['000001', '000002', '600000']
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    print(f"批量获取 {len(test_stocks)} 只股票的数据...")
    start_time = datetime.now()
    batch_data = data_source.get_batch_daily_data(test_stocks, start_date, end_date)
    time_taken = (datetime.now() - start_time).total_seconds()
    
    success_count = sum(1 for data in batch_data.values() if data)
    print(f"批量获取耗时: {time_taken:.2f}秒")
    print(f"成功获取: {success_count}/{len(test_stocks)} 只股票")
    
    for stock_code, data in batch_data.items():
        if data:
            print(f"  {stock_code}: {len(data)} 条记录")
        else:
            print(f"  {stock_code}: 无数据")
    
    # 测试缓存清理
    print("\n4. 测试缓存清理功能")
    print("清理前缓存状态:")
    print(f"  股票列表缓存: {'有' if data_source._cached_stock_list else '无'}")
    print(f"  交易日历缓存: {len(data_source._cached_trading_calendar)} 年")
    
    data_source.clear_cache()
    
    print("清理后缓存状态:")
    print(f"  股票列表缓存: {'有' if data_source._cached_stock_list else '无'}")
    print(f"  交易日历缓存: {len(data_source._cached_trading_calendar)} 年")


def test_optimized_data_service():
    """测试优化数据服务"""
    print("\n" + "=" * 60)
    print("测试 OptimizedDataService 功能")
    print("=" * 60)
    
    # 创建服务实例
    data_source = AkshareDataSource()
    data_access = DataAccessFactory.create_data_access()
    optimized_service = OptimizedDataService(data_source, data_access)
    
    # 测试统计信息
    print("\n1. 测试数据统计功能")
    stats = optimized_service.get_data_statistics()
    if stats:
        print("数据统计信息:")
        print(f"  总股票数: {stats.get('total_stocks', 0)}")
        print(f"  需要更新的股票数: {stats.get('stocks_need_update', 0)}")
        if stats.get('latest_trading_date'):
            print(f"  最新交易日: {stats['latest_trading_date'].date()}")
        
        cache_status = stats.get('cache_status', {})
        print(f"  缓存状态:")
        print(f"    股票列表: {'已缓存' if cache_status.get('stock_list_cached') else '未缓存'}")
        print(f"    交易日历: {cache_status.get('trading_calendar_cached', 0)} 年")
    
    # 测试最新交易日获取
    print("\n2. 测试最新交易日获取")
    latest_date = optimized_service._get_latest_trading_date()
    if latest_date:
        print(f"最新交易日: {latest_date.date()}")
    else:
        print("无法获取最新交易日")
    
    # 清理资源
    data_access.close_connection()


def test_performance_comparison():
    """性能对比测试"""
    print("\n" + "=" * 60)
    print("性能对比测试")
    print("=" * 60)
    
    # 创建服务实例
    data_source = AkshareDataSource()
    data_access = DataAccessFactory.create_data_access()
    
    # 测试少量股票的处理时间
    test_stocks = ['000001', '000002', '600000', '600036', '000858']
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5)
    
    print(f"\n测试 {len(test_stocks)} 只股票，{(end_date - start_date).days} 天数据获取")
    
    # 传统方式：逐个获取
    print("\n传统方式（逐个获取）:")
    start_time = datetime.now()
    traditional_results = {}
    
    for i, stock_code in enumerate(test_stocks, 1):
        try:
            print(f"  获取 {stock_code} ({i}/{len(test_stocks)})")
            data = data_source.get_daily_data(stock_code, start_date, end_date)
            traditional_results[stock_code] = data
            # 模拟原有的固定延时
            import time
            time.sleep(0.1)
        except Exception as e:
            print(f"  {stock_code} 获取失败: {e}")
            traditional_results[stock_code] = []
    
    traditional_time = (datetime.now() - start_time).total_seconds()
    traditional_success = sum(1 for data in traditional_results.values() if data)
    
    print(f"传统方式结果:")
    print(f"  耗时: {traditional_time:.2f}秒")
    print(f"  成功: {traditional_success}/{len(test_stocks)}")
    
    # 优化方式：批量获取
    print("\n优化方式（批量获取）:")
    start_time = datetime.now()
    optimized_results = data_source.get_batch_daily_data(test_stocks, start_date, end_date)
    optimized_time = (datetime.now() - start_time).total_seconds()
    optimized_success = sum(1 for data in optimized_results.values() if data)
    
    print(f"优化方式结果:")
    print(f"  耗时: {optimized_time:.2f}秒")
    print(f"  成功: {optimized_success}/{len(test_stocks)}")
    
    # 性能对比
    if optimized_time > 0:
        speedup = traditional_time / optimized_time
        print(f"\n性能提升: {speedup:.1f}x")
        print(f"时间节省: {traditional_time - optimized_time:.2f}秒 ({(1-optimized_time/traditional_time)*100:.1f}%)")
    
    # 清理资源
    data_access.close_connection()


def main():
    """主测试函数"""
    # 设置日志
    logger = setup_logger(
        name="test_optimized",
        log_level="INFO",
        log_file="logs/test_optimized.log"
    )
    
    print("A股选股系统 - 优化数据获取功能测试")
    print("测试开始时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 测试akshare数据源优化
        test_akshare_source_optimizations()
        
        # 测试优化数据服务
        test_optimized_data_service()
        
        # 性能对比测试
        test_performance_comparison()
        
        print("\n" + "=" * 60)
        print("所有测试完成")
        print("测试结束时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
