#!/usr/bin/env python3
"""
调试批量增量数据获取功能
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.composite_source import CompositeDataSource
from src.data.access.mysql_access import MySQLDataAccess
from src.utils.logger import setup_logger


def debug_batch_incremental():
    """调试批量增量数据获取"""
    # 设置日志
    logger = setup_logger(
        name="debug_batch_incremental",
        log_level="DEBUG",
        log_file="logs/debug_batch_incremental.log"
    )
    
    try:
        logger.info("开始调试批量增量数据获取功能...")
        
        # 创建数据源和数据访问层
        data_source = CompositeDataSource()
        data_access = MySQLDataAccess()
        
        logger.info(f"数据源: {data_source.get_data_source_name()}")
        
        # 获取测试股票
        test_stocks = ['000001', '000002', '000004']  # 测试3只股票
        logger.info(f"测试股票: {test_stocks}")
        
        # 获取最新交易日
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        trading_dates = data_source.get_trading_calendar(start_date, end_date)
        
        if not trading_dates:
            logger.error("无法获取交易日历")
            return False
            
        target_date = max(trading_dates)
        logger.info(f"目标日期: {target_date.date()}")
        
        # 测试批量获取最新日期
        logger.info("测试批量获取最新日期...")
        try:
            latest_dates = data_access.batch_get_latest_trade_dates(test_stocks)
            logger.info(f"最新日期结果: {latest_dates}")
        except Exception as e:
            logger.error(f"批量获取最新日期失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
        
        # 准备批量请求
        requests = []
        for stock_code in test_stocks:
            latest_date = latest_dates.get(stock_code)
            if latest_date and latest_date >= target_date:
                logger.info(f"股票 {stock_code} 数据已是最新")
                continue
            
            start_date_req = latest_date + timedelta(days=1) if latest_date else target_date
            requests.append((stock_code, start_date_req, target_date))
            logger.info(f"股票 {stock_code}: {start_date_req.date()} 到 {target_date.date()}")
        
        if not requests:
            logger.info("所有股票数据都是最新的")
            return True
        
        # 测试批量获取数据
        logger.info(f"测试批量获取数据，请求数量: {len(requests)}")
        try:
            results = data_source.batch_get_daily_data(requests)
            logger.info("批量获取数据成功")
            
            for stock_code, data in results.items():
                if data:
                    logger.info(f"  {stock_code}: {len(data)} 条记录")
                    if data:
                        latest = data[-1]
                        logger.info(f"    最新数据: {latest['trade_date'].strftime('%Y-%m-%d')} "
                                  f"收盘价: {latest['close_price']}")
                else:
                    logger.info(f"  {stock_code}: 无数据")
            
            return True
            
        except Exception as e:
            logger.error(f"批量获取数据失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        logger.error(f"调试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = debug_batch_incremental()
    sys.exit(0 if success else 1)
