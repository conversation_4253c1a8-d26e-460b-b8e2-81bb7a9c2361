#!/usr/bin/env python3
"""
自定义股票策略可视化验证脚本
用户可以修改参数来验证特定股票和时间周期
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig
from src.utils.logger import setup_logger


def custom_validation(stock_code, start_date_str, end_date_str, strategy_name='technical_reversal'):
    """
    自定义股票策略验证
    
    Args:
        stock_code: 股票代码，如 '601111'
        start_date_str: 开始日期，格式 'YYYY-MM-DD'
        end_date_str: 结束日期，格式 'YYYY-MM-DD'
        strategy_name: 策略名称，默认 'technical_reversal'
    """
    logger = setup_logger('custom_validation')
    
    print("🎯 自定义股票策略可视化验证")
    print("="*60)
    print(f"📊 股票代码: {stock_code}")
    print(f"📅 验证期间: {start_date_str} 至 {end_date_str}")
    print(f"🎯 使用策略: {strategy_name}")
    print("="*60)
    
    try:
        # 转换日期格式
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        
        # 创建数据访问和验证器
        data_access = MySQLDataAccess()
        validator = VisualValidator(data_access)
        
        # 获取股票名称
        stock_info = data_access.get_stock_info(stock_code)
        stock_name = stock_info.get('stock_name', stock_code) if stock_info else stock_code
        
        # 配置验证参数
        config = VisualValidationConfig(
            stock_code=stock_code,
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            chart_title=f'{stock_name}({stock_code}) - {strategy_name}策略验证',
            show_indicators=True,
            show_volume=True,
            figure_size=(16, 12)
        )
        
        print("🔍 正在执行验证...")
        
        # 执行验证
        result = validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 验证失败: {result.error_message}")
            return False
        
        # 显示结果
        print("✅ 验证完成!")
        print(f"📈 股票名称: {result.stock_name}")
        print(f"🎯 发现信号: {result.total_signals} 个")
        
        if result.chart_path:
            print(f"📊 图表文件: {result.chart_path}")
            
            # 检查文件大小
            if os.path.exists(result.chart_path):
                file_size = os.path.getsize(result.chart_path) / 1024  # KB
                print(f"📁 文件大小: {file_size:.1f} KB")
        
        # 显示信号详情
        if result.signal_points:
            print(f"\n📋 策略信号详情:")
            print("-" * 80)
            print(f"{'序号':<4} {'日期':<12} {'价格':<8} {'评分':<8} {'RSI':<8} {'量比':<8} {'原因'}")
            print("-" * 80)
            
            for i, signal in enumerate(result.signal_points, 1):
                print(f"{i:<4} "
                      f"{signal.date.date():<12} "
                      f"{signal.price:<8.2f} "
                      f"{signal.score:<8.1f} "
                      f"{signal.indicators.get('rsi', 0):<8.1f} "
                      f"{signal.indicators.get('volume_ratio', 0):<8.2f} "
                      f"{signal.reason}")
        else:
            print("\n📋 在指定期间内未发现符合策略条件的信号点")
        
        # 显示验证总结
        print(f"\n📊 验证总结:")
        print("-" * 60)
        print(result.validation_summary)
        
        print(f"\n🎉 验证完成！请查看生成的图表文件:")
        print(f"📈 {result.chart_path}")
        
        return True
        
    except ValueError as e:
        print(f"❌ 日期格式错误: {str(e)}")
        print("💡 请使用 YYYY-MM-DD 格式，如: 2024-04-01")
        return False
    except Exception as e:
        logger.error(f"验证失败: {str(e)}")
        print(f"❌ 验证过程出错: {str(e)}")
        return False


def main():
    """主函数 - 用户可以在这里修改验证参数"""
    
    # ========== 用户自定义参数区域 ==========
    
    # 股票代码（必填）
    STOCK_CODE = "601111"  # 中国国航
    
    # 验证时间范围（必填）
    START_DATE = "2024-04-01"  # 开始日期
    END_DATE = "2024-04-30"    # 结束日期
    
    # 策略名称（可选，默认技术反转策略）
    STRATEGY_NAME = "technical_reversal"
    
    # ========================================
    
    print("🚀 启动自定义股票策略验证")
    
    # 检查参数
    if not STOCK_CODE:
        print("❌ 请设置股票代码 STOCK_CODE")
        return
    
    if not START_DATE or not END_DATE:
        print("❌ 请设置验证时间范围 START_DATE 和 END_DATE")
        return
    
    # 执行验证
    success = custom_validation(STOCK_CODE, START_DATE, END_DATE, STRATEGY_NAME)
    
    if success:
        print("\n✅ 自定义验证成功完成!")
    else:
        print("\n❌ 自定义验证失败!")


def command_line_validation():
    """命令行参数验证"""
    if len(sys.argv) < 4:
        print("📋 使用方法:")
        print("python scripts/custom_validation.py <股票代码> <开始日期> <结束日期> [策略名称]")
        print("\n📝 示例:")
        print("python scripts/custom_validation.py 601111 2024-04-01 2024-04-30")
        print("python scripts/custom_validation.py 600036 2024-03-01 2024-03-31 technical_reversal")
        print("\n💡 或者直接运行脚本使用预设参数:")
        print("python scripts/custom_validation.py")
        return
    
    stock_code = sys.argv[1]
    start_date = sys.argv[2]
    end_date = sys.argv[3]
    strategy_name = sys.argv[4] if len(sys.argv) > 4 else 'technical_reversal'
    
    print(f"🎯 命令行验证模式")
    success = custom_validation(stock_code, start_date, end_date, strategy_name)
    
    if success:
        print("\n✅ 命令行验证成功完成!")
    else:
        print("\n❌ 命令行验证失败!")


if __name__ == '__main__':
    if len(sys.argv) > 1:
        # 命令行参数模式
        command_line_validation()
    else:
        # 预设参数模式
        main()
