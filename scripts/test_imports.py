#!/usr/bin/env python3
"""
测试导入
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("开始测试导入...")

try:
    print("1. 导入CompositeDataSource...")
    from src.data.sources.composite_source import CompositeDataSource
    print("   成功")
    
    print("2. 导入MySQLDataAccess...")
    from src.data.access.mysql_access import MySQLDataAccess
    print("   成功")
    
    print("3. 导入OptimizedDataService...")
    from src.data.services.optimized_data_service import OptimizedDataService
    print("   成功")
    
    print("4. 创建CompositeDataSource...")
    data_source = CompositeDataSource()
    print(f"   成功: {data_source.get_data_source_name()}")
    
    print("5. 创建MySQLDataAccess...")
    data_access = MySQLDataAccess()
    print("   成功")
    
    print("6. 创建OptimizedDataService...")
    optimized_service = OptimizedDataService(data_source, data_access)
    print("   成功")
    
    print("所有导入和初始化测试通过！")
    
except Exception as e:
    print(f"测试失败: {str(e)}")
    import traceback
    print(f"详细错误: {traceback.format_exc()}")
    sys.exit(1)
