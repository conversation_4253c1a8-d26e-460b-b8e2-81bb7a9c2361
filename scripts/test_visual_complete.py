#!/usr/bin/env python3
"""
完整的可视化验证测试脚本
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig, SignalPoint
from src.utils.logger import setup_logger


def test_complete_visual_validation():
    """测试完整的可视化验证流程"""
    logger = setup_logger('test_complete_visual_validation')
    
    try:
        # 创建数据访问实例
        data_access = MySQLDataAccess()
        visual_validator = VisualValidator(data_access)
        
        # 创建一些模拟信号点用于测试图表生成
        signal_points = [
            SignalPoint(
                date=datetime(2024, 4, 7),
                price=7.50,
                signal_type='buy',
                score=85.5,
                indicators={'rsi': 25.3, 'volume_ratio': 1.8, 'price_position': 0.15},
                reason='布林带下轨突破+成交量放大'
            ),
            SignalPoint(
                date=datetime(2024, 4, 15),
                price=7.80,
                signal_type='buy', 
                score=78.2,
                indicators={'rsi': 28.7, 'volume_ratio': 1.6, 'price_position': 0.22},
                reason='RSI超卖+成交量异常'
            )
        ]
        
        # 获取股票数据
        stock_code = '601111'
        start_date = datetime(2024, 4, 1)
        end_date = datetime(2024, 4, 30)
        
        stock_data = data_access.get_stock_data(stock_code, start_date, end_date)
        logger.info(f"获取到 {len(stock_data)} 条交易数据")
        
        if len(stock_data) < 10:
            logger.warning("交易数据不足，无法生成有效图表")
            return False
        
        # 创建配置
        config = VisualValidationConfig(
            stock_code=stock_code,
            strategy_name='technical_reversal',
            start_date=start_date,
            end_date=end_date,
            chart_title='测试图表 - 中国国航技术反转策略',
            show_indicators=True,
            show_volume=True,
            figure_size=(14, 10)
        )
        
        # 生成图表
        logger.info("开始生成图表...")
        chart_path = visual_validator.generate_chart(stock_code, stock_data, signal_points, config)
        
        logger.info(f"图表生成成功: {chart_path}")
        
        # 验证文件是否存在
        if os.path.exists(chart_path):
            file_size = os.path.getsize(chart_path)
            logger.info(f"图表文件大小: {file_size} 字节")
            return True
        else:
            logger.error("图表文件未生成")
            return False
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_full_visual_validation():
    """测试完整的可视化验证功能"""
    logger = setup_logger('test_full_visual_validation')
    
    try:
        # 创建数据访问实例
        data_access = MySQLDataAccess()
        visual_validator = VisualValidator(data_access)
        
        # 配置验证参数
        config = VisualValidationConfig(
            stock_code='601111',
            strategy_name='technical_reversal',
            start_date=datetime(2024, 4, 1),
            end_date=datetime(2024, 4, 30),
            chart_title='完整验证测试 - 中国国航',
            show_indicators=True,
            show_volume=True
        )
        
        logger.info("开始完整可视化验证...")
        
        # 执行验证
        result = visual_validator.validate_stock_visual(config)
        
        if result.error_message:
            logger.error(f"验证失败: {result.error_message}")
            return False
        
        logger.info(f"验证成功！")
        logger.info(f"股票名称: {result.stock_name}")
        logger.info(f"信号数量: {result.total_signals}")
        
        if result.chart_path:
            logger.info(f"图表路径: {result.chart_path}")
        
        # 输出验证总结
        print("\n" + "="*60)
        print(result.validation_summary)
        print("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == '__main__':
    print("开始完整可视化验证测试...")
    
    # 测试1: 图表生成功能
    print("\n1. 测试图表生成功能...")
    if test_complete_visual_validation():
        print("✓ 图表生成测试通过")
    else:
        print("✗ 图表生成测试失败")
        sys.exit(1)
    
    # 测试2: 完整验证流程
    print("\n2. 测试完整验证流程...")
    if test_full_visual_validation():
        print("✓ 完整验证流程测试通过")
    else:
        print("✗ 完整验证流程测试失败")
    
    print("\n所有测试完成！")
    print("可视化策略验证功能已成功实现！")
