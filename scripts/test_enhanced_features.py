#!/usr/bin/env python3
"""
测试增强的可视化验证功能
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.enhanced_visual_validator import EnhancedVisualValidator
from src.utils.logger import setup_logger


def test_enhanced_validator():
    """测试增强验证器"""
    logger = setup_logger('test_enhanced')
    
    try:
        print("🧪 测试增强的可视化验证功能")
        print("="*50)
        
        # 创建增强验证器
        data_access = MySQLDataAccess()
        validator = EnhancedVisualValidator(data_access)
        print("✅ 增强验证器创建成功")
        
        # 测试批量验证
        print("\n📊 测试批量验证功能...")
        test_stocks = ['601111', '600036']
        
        results = validator.batch_validate_stocks(
            stock_codes=test_stocks,
            strategy_name='technical_reversal',
            start_date=datetime(2024, 4, 1),
            end_date=datetime(2024, 4, 30),
            save_charts=True
        )
        
        print(f"✅ 批量验证完成，验证了 {len(results)} 只股票")
        
        # 生成报告
        print("\n📋 生成批量验证报告...")
        report = validator.generate_batch_report(results)
        print("✅ 报告生成成功")
        
        # 显示简化报告
        successful_count = len([r for r in results if not r.error_message])
        total_signals = sum(r.total_signals for r in results if not r.error_message)
        
        print(f"\n📈 验证结果摘要:")
        print(f"  成功验证: {successful_count}/{len(results)}")
        print(f"  总信号数: {total_signals}")
        
        # 保存结果
        print("\n💾 保存验证结果...")
        results_file = validator.save_validation_results(results)
        print(f"✅ 结果已保存: {results_file}")
        
        # 检查生成的文件
        print(f"\n📁 检查生成的文件:")
        
        # 检查图表文件
        charts_dir = 'charts'
        if os.path.exists(charts_dir):
            chart_files = [f for f in os.listdir(charts_dir) if f.startswith('batch_')]
            print(f"  📊 图表文件: {len(chart_files)} 个")
        
        # 检查结果文件
        if os.path.exists(results_file):
            file_size = os.path.getsize(results_file) / 1024  # KB
            print(f"  📋 结果文件: {file_size:.1f} KB")
        
        print(f"\n🎉 增强功能测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_basic_functionality():
    """测试基础功能"""
    print("\n🔧 测试基础功能...")
    
    try:
        from src.validation.visual_validator import VisualValidator
        from src.core.interfaces.visual_validator import VisualValidationConfig
        
        data_access = MySQLDataAccess()
        validator = VisualValidator(data_access)
        
        # 简单验证测试
        config = VisualValidationConfig(
            stock_code='601111',
            strategy_name='technical_reversal',
            start_date=datetime(2024, 4, 15),
            end_date=datetime(2024, 4, 20),
            show_indicators=True,
            show_volume=True
        )
        
        result = validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 基础功能测试失败: {result.error_message}")
            return False
        else:
            print(f"✅ 基础功能测试成功")
            print(f"  股票: {result.stock_name}")
            print(f"  信号: {result.total_signals} 个")
            return True
            
    except Exception as e:
        print(f"❌ 基础功能测试异常: {str(e)}")
        return False


if __name__ == '__main__':
    print("🚀 开始测试可视化验证增强功能")
    
    # 测试基础功能
    if test_basic_functionality():
        print("✅ 基础功能测试通过")
    else:
        print("❌ 基础功能测试失败")
        sys.exit(1)
    
    # 测试增强功能
    if test_enhanced_validator():
        print("✅ 增强功能测试通过")
    else:
        print("❌ 增强功能测试失败")
    
    print("\n🎉 所有测试完成!")
    print("\n📋 功能清单:")
    print("  ✅ 基础可视化验证")
    print("  ✅ 增强批量验证")
    print("  ✅ 验证报告生成")
    print("  ✅ 结果保存功能")
    print("  ✅ 图表文件生成")
    
    print("\n💡 使用建议:")
    print("  🔧 运行交互式界面: python scripts/interactive_visual_validation.py")
    print("  📊 查看完整演示: python scripts/complete_visual_demo.py")
    print("  📁 查看生成文件: charts/ 和 results/ 目录")
