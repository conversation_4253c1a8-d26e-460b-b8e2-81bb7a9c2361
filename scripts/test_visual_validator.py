#!/usr/bin/env python3
"""
测试可视化验证器的简化脚本
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.utils.logger import setup_logger


def test_data_access():
    """测试数据访问"""
    logger = setup_logger('test_visual_validator')
    
    try:
        # 创建数据访问实例
        data_access = MySQLDataAccess()
        logger.info("数据访问实例创建成功")
        
        # 测试获取股票信息
        stock_info = data_access.get_stock_info('601111')
        logger.info(f"股票信息: {stock_info}")
        
        # 测试获取交易数据
        start_date = datetime(2024, 3, 15)
        end_date = datetime(2024, 3, 20)
        
        stock_data = data_access.get_stock_data('601111', start_date, end_date)
        logger.info(f"获取到 {len(stock_data)} 条交易数据")
        
        if stock_data:
            logger.info("数据样例:")
            for i, data in enumerate(stock_data[:3]):
                logger.info(f"  {i+1}. {data}")
                
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return False


def test_visual_validator_basic():
    """测试可视化验证器基本功能"""
    logger = setup_logger('test_visual_validator_basic')
    
    try:
        from src.validation.visual_validator import VisualValidator
        from src.core.interfaces.visual_validator import VisualValidationConfig
        
        # 创建数据访问实例
        data_access = MySQLDataAccess()
        
        # 创建可视化验证器
        visual_validator = VisualValidator(data_access)
        logger.info("可视化验证器创建成功")
        
        # 测试查找策略信号
        stock_code = '601111'
        strategy_name = 'technical_reversal'
        start_date = datetime(2024, 4, 1)
        end_date = datetime(2024, 4, 10)
        
        logger.info(f"开始查找策略信号: {stock_code}, {strategy_name}")
        signal_points = visual_validator.find_strategy_signals(
            stock_code, strategy_name, start_date, end_date
        )
        
        logger.info(f"找到 {len(signal_points)} 个信号点")
        for signal in signal_points:
            logger.info(f"  信号: {signal.date.date()}, 价格: {signal.price}, 评分: {signal.score}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_chart_generation():
    """测试图表生成"""
    logger = setup_logger('test_chart_generation')
    
    try:
        from src.validation.visual_validator import VisualValidator
        from src.core.interfaces.visual_validator import VisualValidationConfig
        
        # 创建数据访问实例
        data_access = MySQLDataAccess()
        visual_validator = VisualValidator(data_access)
        
        # 获取测试数据
        stock_code = '601111'
        start_date = datetime(2024, 4, 1)
        end_date = datetime(2024, 4, 30)
        
        stock_data = data_access.get_stock_data(stock_code, start_date, end_date)
        logger.info(f"获取到 {len(stock_data)} 条交易数据")
        
        if not stock_data:
            logger.warning("没有交易数据，跳过图表生成测试")
            return False
        
        # 准备图表数据
        df = visual_validator._prepare_chart_data(stock_data)
        logger.info(f"准备图表数据成功，数据形状: {df.shape}")
        logger.info(f"数据列: {list(df.columns)}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == '__main__':
    print("开始测试可视化验证器...")
    
    # 测试1: 数据访问
    print("\n1. 测试数据访问...")
    if test_data_access():
        print("✓ 数据访问测试通过")
    else:
        print("✗ 数据访问测试失败")
        sys.exit(1)
    
    # 测试2: 可视化验证器基本功能
    print("\n2. 测试可视化验证器基本功能...")
    if test_visual_validator_basic():
        print("✓ 可视化验证器基本功能测试通过")
    else:
        print("✗ 可视化验证器基本功能测试失败")
    
    # 测试3: 图表生成
    print("\n3. 测试图表生成...")
    if test_chart_generation():
        print("✓ 图表生成测试通过")
    else:
        print("✗ 图表生成测试失败")
    
    print("\n测试完成！")
