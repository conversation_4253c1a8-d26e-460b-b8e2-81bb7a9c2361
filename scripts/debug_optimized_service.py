#!/usr/bin/env python3
"""
调试优化数据服务
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.composite_source import CompositeDataSource
from src.data.access.mysql_access import MySQLDataAccess
from src.data.services.optimized_data_service import OptimizedDataService
from src.utils.logger import setup_logger


def debug_optimized_service():
    """调试优化数据服务"""
    # 设置日志
    logger = setup_logger(
        name="debug_optimized_service",
        log_level="DEBUG",
        log_file="logs/debug_optimized_service.log"
    )
    
    try:
        logger.info("开始调试优化数据服务...")
        
        # 创建数据源和数据访问层
        logger.info("步骤1: 创建数据源...")
        data_source = CompositeDataSource()
        logger.info(f"数据源创建成功: {data_source.get_data_source_name()}")
        
        logger.info("步骤2: 创建数据访问层...")
        data_access = MySQLDataAccess()
        logger.info("数据访问层创建成功")
        
        logger.info("步骤3: 创建优化数据服务...")
        optimized_service = OptimizedDataService(data_source, data_access)
        logger.info("优化数据服务创建成功")
        
        logger.info("步骤4: 获取需要更新的股票列表...")
        stocks_need_update = data_access.get_stocks_need_update()
        logger.info(f"需要更新的股票数量: {len(stocks_need_update) if stocks_need_update else 0}")
        
        if stocks_need_update:
            # 只测试前3只股票
            test_stocks = stocks_need_update[:3]
            logger.info(f"测试股票: {test_stocks}")
            
            logger.info("步骤5: 获取最新交易日...")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            trading_dates = data_source.get_trading_calendar(start_date, end_date)
            
            if trading_dates:
                target_date = max(trading_dates)
                logger.info(f"最新交易日: {target_date.date()}")
                
                logger.info("步骤6: 测试批量处理...")
                success_count = optimized_service._process_batch_incremental(test_stocks, target_date)
                logger.info(f"批量处理结果: 成功 {success_count}/{len(test_stocks)}")
                
            else:
                logger.error("无法获取交易日历")
                return False
        else:
            logger.info("没有需要更新的股票")
        
        logger.info("调试完成")
        return True
        
    except Exception as e:
        logger.error(f"调试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False
    
    finally:
        if 'data_access' in locals():
            data_access.close_connection()


if __name__ == "__main__":
    success = debug_optimized_service()
    sys.exit(0 if success else 1)
