#!/usr/bin/env python3
"""
可视化策略验证功能演示脚本
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig
from src.utils.logger import setup_logger


def demo_single_stock_validation():
    """演示单股票可视化验证"""
    print("\n" + "="*80)
    print("可视化策略验证功能演示")
    print("="*80)
    
    logger = setup_logger('demo_visual_validation')
    
    try:
        # 创建数据访问和验证器
        data_access = MySQLDataAccess()
        visual_validator = VisualValidator(data_access)
        
        # 配置验证参数
        config = VisualValidationConfig(
            stock_code='601111',  # 中国国航
            strategy_name='technical_reversal',  # 技术反转策略
            start_date=datetime(2024, 3, 15),  # 开始日期
            end_date=datetime(2024, 5, 31),    # 结束日期
            chart_title='中国国航(601111) - 技术反转策略验证',
            show_indicators=True,
            show_volume=True,
            figure_size=(16, 12)
        )
        
        print(f"股票代码: {config.stock_code}")
        print(f"策略名称: {config.strategy_name}")
        print(f"验证期间: {config.start_date.date()} 至 {config.end_date.date()}")
        print("\n正在执行可视化验证...")
        
        # 执行验证
        result = visual_validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 验证失败: {result.error_message}")
            return False
        
        print(f"✅ 验证完成！")
        print(f"📊 股票名称: {result.stock_name}")
        print(f"🎯 发现信号: {result.total_signals} 个")
        
        if result.chart_path:
            print(f"📈 图表文件: {result.chart_path}")
            
            # 检查文件大小
            if os.path.exists(result.chart_path):
                file_size = os.path.getsize(result.chart_path) / 1024  # KB
                print(f"📁 文件大小: {file_size:.1f} KB")
        
        # 显示验证总结
        print("\n" + "="*80)
        print("验证总结报告")
        print("="*80)
        print(result.validation_summary)
        
        # 显示信号详情
        if result.signal_points:
            print("\n" + "="*80)
            print("策略信号详情")
            print("="*80)
            print(f"{'序号':<4} {'日期':<12} {'价格':<8} {'评分':<8} {'RSI':<8} {'量比':<8} {'原因'}")
            print("-" * 80)
            
            for i, signal in enumerate(result.signal_points, 1):
                print(f"{i:<4} "
                      f"{signal.date.date():<12} "
                      f"{signal.price:<8.2f} "
                      f"{signal.score:<8.1f} "
                      f"{signal.indicators.get('rsi', 0):<8.1f} "
                      f"{signal.indicators.get('volume_ratio', 0):<8.2f} "
                      f"{signal.reason}")
        
        print("\n" + "="*80)
        print("🎉 可视化策略验证演示完成！")
        print(f"📈 请查看生成的K线图: {result.chart_path}")
        print("="*80)
        
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {str(e)}")
        print(f"❌ 演示失败: {str(e)}")
        return False


def demo_multiple_periods():
    """演示多个时间段的验证"""
    print("\n" + "="*80)
    print("多时间段验证演示")
    print("="*80)
    
    logger = setup_logger('demo_multiple_periods')
    
    # 定义多个测试时间段
    test_periods = [
        ("2024年3月", datetime(2024, 3, 1), datetime(2024, 3, 31)),
        ("2024年4月", datetime(2024, 4, 1), datetime(2024, 4, 30)),
        ("2024年5月", datetime(2024, 5, 1), datetime(2024, 5, 31))
    ]
    
    try:
        data_access = MySQLDataAccess()
        visual_validator = VisualValidator(data_access)
        
        results = []
        
        for period_name, start_date, end_date in test_periods:
            print(f"\n📅 验证时间段: {period_name}")
            
            config = VisualValidationConfig(
                stock_code='601111',
                strategy_name='technical_reversal',
                start_date=start_date,
                end_date=end_date,
                chart_title=f'中国国航 - {period_name}策略验证',
                show_indicators=True,
                show_volume=True
            )
            
            result = visual_validator.validate_stock_visual(config)
            
            if result.error_message:
                print(f"  ❌ {period_name} 验证失败: {result.error_message}")
                continue
            
            results.append((period_name, result))
            print(f"  ✅ {period_name} 验证完成，发现 {result.total_signals} 个信号")
            if result.chart_path:
                print(f"  📈 图表: {result.chart_path}")
        
        # 汇总结果
        print("\n" + "="*80)
        print("多时间段验证汇总")
        print("="*80)
        print(f"{'时间段':<12} {'信号数量':<8} {'图表文件'}")
        print("-" * 80)
        
        total_signals = 0
        for period_name, result in results:
            total_signals += result.total_signals
            chart_name = os.path.basename(result.chart_path) if result.chart_path else "无"
            print(f"{period_name:<12} {result.total_signals:<8} {chart_name}")
        
        print("-" * 80)
        print(f"{'总计':<12} {total_signals:<8}")
        
        return True
        
    except Exception as e:
        logger.error(f"多时间段验证失败: {str(e)}")
        print(f"❌ 多时间段验证失败: {str(e)}")
        return False


if __name__ == '__main__':
    print("🚀 启动可视化策略验证功能演示")
    
    # 演示1: 单股票验证
    if demo_single_stock_validation():
        print("\n✅ 单股票验证演示成功")
    else:
        print("\n❌ 单股票验证演示失败")
        sys.exit(1)
    
    # 演示2: 多时间段验证
    if demo_multiple_periods():
        print("\n✅ 多时间段验证演示成功")
    else:
        print("\n❌ 多时间段验证演示失败")
    
    print("\n🎉 所有演示完成！")
    print("\n📋 功能特点:")
    print("  • 可视化K线图表")
    print("  • 策略信号标注")
    print("  • 技术指标显示")
    print("  • 成交量分析")
    print("  • 详细验证报告")
    print("\n📁 生成的图表文件保存在 charts/ 目录下")
