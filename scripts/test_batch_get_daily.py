#!/usr/bin/env python3
"""
测试批量获取日线数据功能
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.composite_source import CompositeDataSource
from src.utils.logger import setup_logger


def test_batch_get_daily():
    """测试批量获取日线数据"""
    # 设置日志
    logger = setup_logger(
        name="test_batch",
        log_level="DEBUG",
        log_file="logs/test_batch.log"
    )
    
    try:
        logger.info("开始测试批量获取日线数据功能...")
        
        # 创建数据源
        data_source = CompositeDataSource()
        logger.info(f"数据源: {data_source.get_data_source_name()}")
        
        # 准备测试数据
        test_stocks = ['000001', '000002', '600000']  # 测试3只股票
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)  # 最近7天
        
        logger.info(f"测试股票: {test_stocks}")
        logger.info(f"日期范围: {start_date.date()} 到 {end_date.date()}")
        
        # 准备批量请求
        requests = [(stock_code, start_date, end_date) for stock_code in test_stocks]
        
        # 测试批量获取
        logger.info("开始批量获取数据...")
        results = data_source.batch_get_daily_data(requests)
        
        # 显示结果
        logger.info("批量获取结果:")
        for stock_code, data in results.items():
            if data:
                logger.info(f"  {stock_code}: {len(data)} 条记录")
                if data:
                    latest = data[-1]
                    logger.info(f"    最新数据: {latest['trade_date'].strftime('%Y-%m-%d')} "
                              f"收盘价: {latest['close_price']}")
            else:
                logger.info(f"  {stock_code}: 无数据")
        
        logger.info("批量获取测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = test_batch_get_daily()
    sys.exit(0 if success else 1)
