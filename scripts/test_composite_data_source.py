"""
测试组合数据源（akshare交易日历 + tushare日线数据）
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.data.sources.composite_source import CompositeDataSource
from datetime import datetime

def test_composite_data_source():
    print("开始测试组合数据源...")
    data_source = CompositeDataSource()
    
    # 测试获取交易日历
    print("\n测试获取交易日历:")
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 1, 31)
    trade_dates = data_source.get_trading_calendar(start_date, end_date)
    print(f"获取到 {len(trade_dates)} 个交易日")
    print(f"前5个交易日: {trade_dates[:5]}")
    
    # 测试获取日线数据
    print("\n测试获取日线数据:")
    daily_data = data_source.get_daily_data("000001.SZ", start_date, end_date)
    print(f"获取到 {len(daily_data)} 条日线数据")
    
    # 检查是否有缺失数据
    missing_days = [d['trade_date'] for d in daily_data if d.get('is_missing', False)]
    print(f"缺失数据天数: {len(missing_days)}")
    
    # 打印部分数据
    if daily_data:
        print("\n前5条日线数据:")
        for data in daily_data[:5]:
            print(f"{data['trade_date']}: 开盘{data['open_price']}, 收盘{data['close_price']}, 是否缺失:{data.get('is_missing', False)}")
    
    print("\n组合数据源测试完成!")

if __name__ == "__main__":
    test_composite_data_source()
