#!/usr/bin/env python3
"""
完整的可视化策略验证功能演示
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.enhanced_visual_validator import EnhancedVisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig
from src.utils.logger import setup_logger


def demo_enhanced_features():
    """演示增强功能"""
    print("🚀 可视化策略验证系统 - 完整功能演示")
    print("="*80)
    
    logger = setup_logger('complete_demo')
    
    try:
        # 创建增强验证器
        data_access = MySQLDataAccess()
        validator = EnhancedVisualValidator(data_access)
        
        # 演示1: 单股票详细验证
        print("\n📊 演示1: 单股票详细验证")
        print("-" * 50)
        
        config = VisualValidationConfig(
            stock_code='601111',
            strategy_name='technical_reversal',
            start_date=datetime(2024, 3, 1),
            end_date=datetime(2024, 5, 31),
            chart_title='中国国航 - 技术反转策略完整验证',
            show_indicators=True,
            show_volume=True,
            figure_size=(18, 12)
        )
        
        result = validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 验证失败: {result.error_message}")
        else:
            print(f"✅ 验证成功!")
            print(f"📈 股票: {result.stock_name}({result.config.stock_code})")
            print(f"🎯 信号数: {result.total_signals}")
            print(f"📊 图表: {result.chart_path}")
            
            if result.signal_points:
                print(f"\n🔍 信号详情:")
                for i, signal in enumerate(result.signal_points, 1):
                    print(f"  {i}. {signal.date.date()} | "
                          f"价格:{signal.price:.2f} | "
                          f"评分:{signal.score:.1f} | "
                          f"RSI:{signal.indicators.get('rsi', 0):.1f}")
        
        # 演示2: 批量验证
        print(f"\n📊 演示2: 批量股票验证")
        print("-" * 50)
        
        test_stocks = ['601111', '600036', '000001']
        print(f"测试股票: {', '.join(test_stocks)}")
        
        batch_results = validator.batch_validate_stocks(
            stock_codes=test_stocks,
            strategy_name='technical_reversal',
            start_date=datetime(2024, 4, 1),
            end_date=datetime(2024, 4, 30),
            save_charts=True
        )
        
        # 生成批量报告
        batch_report = validator.generate_batch_report(batch_results)
        print("\n📋 批量验证报告:")
        print(batch_report)
        
        # 保存结果
        results_file = validator.save_validation_results(batch_results)
        print(f"\n💾 验证结果已保存: {results_file}")
        
        # 演示3: 策略比较
        print(f"\n📊 演示3: 策略效果比较")
        print("-" * 50)
        
        comparison_stocks = ['601111', '600036']
        strategies = ['technical_reversal']  # 可以添加更多策略
        
        print(f"比较股票: {', '.join(comparison_stocks)}")
        print(f"比较策略: {', '.join(strategies)}")
        
        strategy_results = validator.compare_strategies(
            stock_codes=comparison_stocks,
            strategy_names=strategies,
            start_date=datetime(2024, 4, 1),
            end_date=datetime(2024, 4, 30)
        )
        
        comparison_report = validator.generate_strategy_comparison_report(strategy_results)
        print("\n📋 策略比较报告:")
        print(comparison_report)
        
        # 演示4: 时间段分析
        print(f"\n📊 演示4: 不同时间段效果分析")
        print("-" * 50)
        
        time_periods = [
            ("2024年3月", datetime(2024, 3, 1), datetime(2024, 3, 31)),
            ("2024年4月", datetime(2024, 4, 1), datetime(2024, 4, 30)),
            ("2024年5月", datetime(2024, 5, 1), datetime(2024, 5, 31))
        ]
        
        period_results = {}
        for period_name, start_date, end_date in time_periods:
            print(f"\n分析时间段: {period_name}")
            
            period_result = validator.batch_validate_stocks(
                stock_codes=['601111'],
                strategy_name='technical_reversal',
                start_date=start_date,
                end_date=end_date,
                save_charts=False
            )
            
            if period_result and not period_result[0].error_message:
                signals = period_result[0].total_signals
                print(f"  发现信号: {signals} 个")
                period_results[period_name] = signals
            else:
                print(f"  验证失败或无数据")
                period_results[period_name] = 0
        
        # 时间段总结
        print(f"\n📈 时间段信号统计:")
        for period, signals in period_results.items():
            print(f"  {period}: {signals} 个信号")
        
        # 演示5: 生成综合报告
        print(f"\n📊 演示5: 综合分析报告")
        print("-" * 50)
        
        generate_comprehensive_report(validator, batch_results, strategy_results, period_results)
        
        print(f"\n🎉 完整功能演示完成!")
        print(f"📁 生成的文件:")
        print(f"  📊 图表文件: charts/ 目录")
        print(f"  📋 验证结果: {results_file}")
        print(f"  📈 综合报告: comprehensive_report.txt")
        
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {str(e)}")
        print(f"❌ 演示过程出错: {str(e)}")
        return False


def generate_comprehensive_report(validator, batch_results, strategy_results, period_results):
    """生成综合分析报告"""
    report = []
    report.append("=" * 100)
    report.append("可视化策略验证系统 - 综合分析报告")
    report.append("=" * 100)
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 批量验证总结
    report.append(f"\n📊 批量验证总结:")
    successful_results = [r for r in batch_results if not r.error_message]
    total_signals = sum(r.total_signals for r in successful_results)
    
    report.append(f"验证股票数: {len(batch_results)}")
    report.append(f"成功验证数: {len(successful_results)}")
    report.append(f"总信号数: {total_signals}")
    report.append(f"平均信号数: {total_signals/len(successful_results) if successful_results else 0:.1f}")
    
    # 时间段分析
    report.append(f"\n📈 时间段分析:")
    for period, signals in period_results.items():
        report.append(f"{period}: {signals} 个信号")
    
    # 最佳表现股票
    if successful_results:
        best_stock = max(successful_results, key=lambda x: x.total_signals)
        report.append(f"\n🏆 最佳表现股票:")
        report.append(f"股票: {best_stock.stock_name}({best_stock.config.stock_code})")
        report.append(f"信号数: {best_stock.total_signals}")
        
        if best_stock.signal_points:
            best_signal = max(best_stock.signal_points, key=lambda x: x.score)
            report.append(f"最佳信号: {best_signal.date.date()}, 评分: {best_signal.score:.1f}")
    
    # 策略效果评估
    report.append(f"\n🎯 策略效果评估:")
    if total_signals > 0:
        report.append("✅ 策略能够有效识别交易信号")
        report.append("✅ 可视化验证功能运行正常")
        report.append("✅ 图表生成功能完整")
    else:
        report.append("⚠️ 在测试期间未发现明显信号")
        report.append("💡 建议调整策略参数或扩大测试范围")
    
    # 系统功能验证
    report.append(f"\n✅ 系统功能验证:")
    report.append("✅ 单股票可视化验证")
    report.append("✅ 批量股票验证")
    report.append("✅ 策略效果比较")
    report.append("✅ 时间段分析")
    report.append("✅ 验证结果保存")
    report.append("✅ 图表生成和标注")
    report.append("✅ 详细报告生成")
    
    # 使用建议
    report.append(f"\n💡 使用建议:")
    report.append("1. 定期使用批量验证功能检验策略效果")
    report.append("2. 通过时间段分析了解策略的时效性")
    report.append("3. 利用可视化图表直观分析信号质量")
    report.append("4. 保存验证结果便于历史对比分析")
    report.append("5. 结合多个策略进行效果比较")
    
    # 保存报告
    with open('comprehensive_report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("📋 综合报告已生成: comprehensive_report.txt")


def quick_demo():
    """快速演示核心功能"""
    print("⚡ 快速演示模式")
    print("-" * 40)
    
    try:
        data_access = MySQLDataAccess()
        validator = EnhancedVisualValidator(data_access)
        
        # 快速单股票验证
        config = VisualValidationConfig(
            stock_code='601111',
            strategy_name='technical_reversal',
            start_date=datetime(2024, 4, 1),
            end_date=datetime(2024, 4, 30),
            show_indicators=True,
            show_volume=True
        )
        
        result = validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 快速验证失败: {result.error_message}")
        else:
            print(f"✅ 快速验证成功!")
            print(f"📈 {result.stock_name}: {result.total_signals} 个信号")
            print(f"📊 图表: {result.chart_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速演示失败: {str(e)}")
        return False


if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'quick':
        quick_demo()
    else:
        demo_enhanced_features()
