#!/usr/bin/env python3
"""
测试Tushare数据源功能
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.tushare_source import TushareDataSource
from src.config.tushare_config import tushare_config
from src.utils.logger import setup_logger


def test_tushare_token():
    """测试tushare token配置"""
    print("=" * 60)
    print("测试 Tushare Token 配置")
    print("=" * 60)

    try:
        # 检查token是否配置
        if tushare_config.is_token_configured():
            print(f"✓ Tushare token已配置")
            print(f"Token前缀: {tushare_config.get_token()[:10]}...")
        else:
            print("✗ Tushare token未配置")
            print("请运行以下命令进行配置:")
            print("  python scripts/setup_tushare_token.py")
            return False

        return True

    except Exception as e:
        print(f"✗ Token配置检查失败: {str(e)}")
        return False


def test_tushare_connection():
    """测试tushare连接"""
    print("\n" + "=" * 60)
    print("测试 Tushare 连接")
    print("=" * 60)

    try:
        data_source = TushareDataSource()
        print(f"✓ 数据源初始化成功: {data_source.get_data_source_name()}")
        return data_source

    except Exception as e:
        print(f"✗ 数据源初始化失败: {str(e)}")
        return None


def test_stock_list(data_source):
    """测试获取股票列表"""
    print("\n" + "=" * 60)
    print("测试获取股票列表")
    print("=" * 60)

    try:
        print("获取股票列表...")
        stock_list = data_source.get_stock_list(use_cache=False)

        if stock_list:
            print(f"✓ 成功获取股票列表，共 {len(stock_list)} 只股票")

            # 显示前5只股票信息
            print("\n前5只股票信息:")
            for i, stock in enumerate(stock_list[:5]):
                print(f"{i+1}. {stock['stock_code']} - {stock['stock_name']} - {stock['market']}")

            return True
        else:
            print("✗ 获取股票列表失败：返回空列表")
            return False

    except Exception as e:
        print(f"✗ 获取股票列表失败: {str(e)}")
        return False


def test_daily_data(data_source):
    """测试获取日K线数据"""
    print("\n" + "=" * 60)
    print("测试获取日K线数据")
    print("=" * 60)

    try:
        # 测试股票：平安银行 000001
        stock_code = "000001"
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        print(f"获取股票 {stock_code} 最近30天的数据...")
        daily_data = data_source.get_daily_data(stock_code, start_date, end_date)

        if daily_data:
            print(f"✓ 成功获取日K线数据，共 {len(daily_data)} 条记录")

            # 显示最近3天的数据
            print("\n最近3天的数据:")
            for i, data in enumerate(daily_data[-3:]):
                print(f"{i+1}. {data['trade_date'].strftime('%Y-%m-%d')} - "
                      f"开盘: {data['open_price']:.2f}, "
                      f"收盘: {data['close_price']:.2f}, "
                      f"成交量: {data['volume']}")

            return True
        else:
            print("✗ 获取日K线数据失败：返回空列表")
            return False

    except Exception as e:
        print(f"✗ 获取日K线数据失败: {str(e)}")
        return False


def test_stock_validation(data_source):
    """测试股票代码验证"""
    print("\n" + "=" * 60)
    print("测试股票代码验证")
    print("=" * 60)

    test_codes = [
        ("000001", "有效代码"),
        ("600036", "有效代码"),
        ("999999", "无效代码"),
        ("abc123", "格式错误")
    ]

    for code, desc in test_codes:
        try:
            is_valid = data_source.validate_stock_code(code)
            status = "✓ 有效" if is_valid else "✗ 无效"
            print(f"{code} ({desc}): {status}")
        except Exception as e:
            print(f"{code} ({desc}): ✗ 验证失败 - {str(e)}")


def test_trading_calendar(data_source):
    """测试交易日历"""
    print("\n" + "=" * 60)
    print("测试交易日历")
    print("=" * 60)

    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        print(f"获取最近30天的交易日历...")
        trading_dates = data_source.get_trading_calendar(start_date, end_date)

        if trading_dates:
            print(f"✓ 成功获取交易日历，共 {len(trading_dates)} 个交易日")

            # 显示最近5个交易日
            print("\n最近5个交易日:")
            for i, date in enumerate(trading_dates[-5:]):
                print(f"{i+1}. {date.strftime('%Y-%m-%d %A')}")

            return True
        else:
            print("✗ 获取交易日历失败：返回空列表")
            return False

    except Exception as e:
        print(f"✗ 获取交易日历失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("Tushare数据源功能测试")
    print("=" * 60)

    # 设置日志
    logger = setup_logger()

    # 测试步骤
    tests = [
        ("Token配置", test_tushare_token),
        ("连接测试", test_tushare_connection),
    ]

    results = {}
    data_source = None

    # 执行基础测试
    for test_name, test_func in tests:
        if test_name == "连接测试":
            data_source = test_func()
            results[test_name] = data_source is not None
        else:
            results[test_name] = test_func()

    # 如果连接成功，执行数据测试
    if data_source:
        data_tests = [
            ("股票列表", lambda: test_stock_list(data_source)),
            ("日K线数据", lambda: test_daily_data(data_source)),
            ("股票验证", lambda: test_stock_validation(data_source)),
            ("交易日历", lambda: test_trading_calendar(data_source)),
        ]

        for test_name, test_func in data_tests:
            results[test_name] = test_func()

    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 所有测试通过！Tushare数据源配置正确。")
        return True
    else:
        print("❌ 部分测试失败，请检查配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
