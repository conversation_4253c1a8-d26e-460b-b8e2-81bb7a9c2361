"""
测试akshare数据源获取数据功能
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from src.data.sources.akshare_source import AkshareDataSource

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_akshare_data_fetch():
    """测试akshare数据获取功能"""
    try:
        logger.info("开始测试akshare数据获取功能...")
        
        # 初始化数据源
        data_source = AkshareDataSource()
        
        logger.info("测试获取股票列表...")
        stock_list = data_source.get_stock_list()
        logger.info(f"成功获取 {len(stock_list)} 只股票信息")
        
        # 选择3只股票进行测试
        test_stocks = [stock['stock_code'] for stock in stock_list[:3]]
        logger.info(f"测试股票: {test_stocks}")
        
        # 设置日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        logger.info("测试获取单只股票日K线数据...")
        for stock_code in test_stocks:
            daily_data = data_source.get_daily_data(stock_code, start_date, end_date)
            logger.info(f"股票 {stock_code} 获取到 {len(daily_data)} 条日K线数据")
        
        logger.info("测试批量获取股票日K线数据...")
        requests = [(stock_code, start_date, end_date) for stock_code in test_stocks]
        batch_data = data_source.batch_get_daily_data(requests)
        for stock_code, data in batch_data.items():
            logger.info(f"批量获取: 股票 {stock_code} 有 {len(data)} 条数据")
        
        logger.info("测试获取交易日历...")
        trading_dates = data_source.get_trading_calendar(start_date, end_date)
        logger.info(f"获取到 {len(trading_dates)} 个交易日")
        
        logger.info("测试获取实时数据...")
        realtime_data = data_source.get_realtime_data(test_stocks)
        for stock_code, data in realtime_data.items():
            if data:
                logger.info(f"股票 {stock_code} 实时数据: 价格={data.get('price')}, 涨跌幅={data.get('change')}%")
            else:
                logger.warning(f"股票 {stock_code} 未获取到实时数据")
        
        logger.info("akshare数据获取测试完成")
        return True
    
    except Exception as e:
        logger.exception("akshare数据获取测试失败")
        return False

if __name__ == "__main__":
    test_akshare_data_fetch()
