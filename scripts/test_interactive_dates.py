#!/usr/bin/env python3
"""
测试交互式界面的日期输入功能
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig


def test_date_validation():
    """测试日期验证功能"""
    print("🧪 测试日期输入和验证功能")
    print("="*50)
    
    # 测试用例
    test_cases = [
        {
            'stock_code': '601111',
            'start_date': '2024-04-01',
            'end_date': '2024-04-30',
            'description': '正常日期范围'
        },
        {
            'stock_code': '601111', 
            'start_date': '2024-04-15',
            'end_date': '2024-04-20',
            'description': '短期日期范围'
        },
        {
            'stock_code': '600036',
            'start_date': '2024-03-01',
            'end_date': '2024-03-31',
            'description': '不同股票和月份'
        }
    ]
    
    try:
        data_access = MySQLDataAccess()
        validator = VisualValidator(data_access)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {test_case['description']}")
            print(f"   股票: {test_case['stock_code']}")
            print(f"   期间: {test_case['start_date']} 至 {test_case['end_date']}")
            
            # 转换日期
            start_date = datetime.strptime(test_case['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(test_case['end_date'], '%Y-%m-%d')
            
            # 验证日期范围
            date_diff = (end_date - start_date).days
            print(f"   天数: {date_diff} 天")
            
            if start_date >= end_date:
                print("   ❌ 日期范围无效")
                continue
            
            # 执行验证
            config = VisualValidationConfig(
                stock_code=test_case['stock_code'],
                strategy_name='technical_reversal',
                start_date=start_date,
                end_date=end_date,
                show_indicators=True,
                show_volume=True
            )
            
            result = validator.validate_stock_visual(config)
            
            if result.error_message:
                print(f"   ❌ 验证失败: {result.error_message}")
            else:
                print(f"   ✅ 验证成功: {result.total_signals} 个信号")
                print(f"   📊 图表: {os.path.basename(result.chart_path) if result.chart_path else '无'}")
        
        print(f"\n🎉 日期验证测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def simulate_interactive_input():
    """模拟交互式输入"""
    print("\n🎭 模拟交互式输入流程")
    print("="*50)
    
    # 模拟用户输入的数据
    mock_inputs = {
        'stock_code': '601111',
        'strategy': '1',
        'start_date': '2024-04-01', 
        'end_date': '2024-04-30'
    }
    
    print("📝 模拟用户输入:")
    for key, value in mock_inputs.items():
        print(f"   {key}: {value}")
    
    try:
        # 验证输入格式
        start_date = datetime.strptime(mock_inputs['start_date'], '%Y-%m-%d')
        end_date = datetime.strptime(mock_inputs['end_date'], '%Y-%m-%d')
        
        print(f"\n✅ 日期解析成功:")
        print(f"   开始日期: {start_date.date()}")
        print(f"   结束日期: {end_date.date()}")
        print(f"   验证天数: {(end_date - start_date).days} 天")
        
        # 执行验证
        data_access = MySQLDataAccess()
        validator = VisualValidator(data_access)
        
        config = VisualValidationConfig(
            stock_code=mock_inputs['stock_code'],
            strategy_name='technical_reversal',
            start_date=start_date,
            end_date=end_date,
            show_indicators=True,
            show_volume=True
        )
        
        print(f"\n🔍 执行验证...")
        result = validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 验证失败: {result.error_message}")
        else:
            print(f"✅ 验证成功!")
            print(f"📈 股票: {result.stock_name}")
            print(f"🎯 信号: {result.total_signals} 个")
            print(f"📊 图表: {result.chart_path}")
            
            # 显示信号详情
            if result.signal_points:
                print(f"\n📋 信号详情:")
                for i, signal in enumerate(result.signal_points, 1):
                    print(f"   {i}. {signal.date.date()} - 价格:{signal.price:.2f} - 评分:{signal.score:.1f}")
        
        return True
        
    except ValueError as e:
        print(f"❌ 日期格式错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 验证异常: {str(e)}")
        return False


def test_date_edge_cases():
    """测试日期边界情况"""
    print("\n🔬 测试日期边界情况")
    print("="*50)
    
    edge_cases = [
        {
            'name': '开始日期晚于结束日期',
            'start': '2024-04-30',
            'end': '2024-04-01',
            'should_fail': True
        },
        {
            'name': '相同的开始和结束日期',
            'start': '2024-04-15',
            'end': '2024-04-15', 
            'should_fail': True
        },
        {
            'name': '超长时间范围',
            'start': '2024-01-01',
            'end': '2024-12-31',
            'should_fail': False,
            'warning': True
        },
        {
            'name': '正常短期范围',
            'start': '2024-04-01',
            'end': '2024-04-07',
            'should_fail': False
        }
    ]
    
    for case in edge_cases:
        print(f"\n📝 测试: {case['name']}")
        
        try:
            start_date = datetime.strptime(case['start'], '%Y-%m-%d')
            end_date = datetime.strptime(case['end'], '%Y-%m-%d')
            
            # 检查日期逻辑
            if start_date >= end_date:
                print("   ❌ 日期范围无效 (开始日期不早于结束日期)")
                if case['should_fail']:
                    print("   ✅ 符合预期 (应该失败)")
                else:
                    print("   ⚠️ 意外失败")
                continue
            
            date_diff = (end_date - start_date).days
            print(f"   📅 日期范围: {date_diff} 天")
            
            if date_diff > 180 and case.get('warning'):
                print("   ⚠️ 时间范围过长，可能影响性能")
            
            print("   ✅ 日期范围有效")
            
        except ValueError as e:
            print(f"   ❌ 日期格式错误: {str(e)}")
    
    print(f"\n🎉 边界情况测试完成!")


def main():
    """主测试函数"""
    print("🚀 交互式日期输入功能测试")
    print("="*60)
    
    # 测试1: 基本日期验证
    print("\n1️⃣ 基本日期验证测试")
    if test_date_validation():
        print("✅ 基本日期验证测试通过")
    else:
        print("❌ 基本日期验证测试失败")
    
    # 测试2: 模拟交互输入
    print("\n2️⃣ 模拟交互输入测试")
    if simulate_interactive_input():
        print("✅ 模拟交互输入测试通过")
    else:
        print("❌ 模拟交互输入测试失败")
    
    # 测试3: 边界情况测试
    print("\n3️⃣ 边界情况测试")
    test_date_edge_cases()
    
    print("\n🎉 所有测试完成!")
    print("\n💡 使用建议:")
    print("   📅 日期格式: YYYY-MM-DD")
    print("   ⏰ 建议时间范围: 1-3个月")
    print("   🔍 开始日期应早于结束日期")
    print("   ⚠️ 避免过长的时间范围")


if __name__ == '__main__':
    main()
