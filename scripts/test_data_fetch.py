"""
测试从远程数据源获取数据并保存的功能
"""
import sys
import os
import logging
import time
from datetime import datetime, timedelta

# 获取项目根目录路径（当前脚本的父目录的父目录）
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 将项目根目录添加到Python路径
sys.path.append(project_root)

from src.data.sources.tushare_source import TushareDataSource
from src.data.services.optimized_data_service import OptimizedDataService
from src.core.interfaces.data_access import IDataAccess
from src.core.exceptions.custom_exceptions import DataSourceError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockDataAccess(IDataAccess):
    """模拟数据访问对象"""
    
    def __init__(self):
        self.saved_data = {}
        self.stock_codes = ['000001', '600000', '000858']  # 测试股票代码
    
    def get_stocks_need_update(self) -> list:
        return self.stock_codes
    
    def get_all_stock_codes(self) -> list:
        return self.stock_codes
    
    def get_latest_trade_date(self, stock_code: str) -> datetime:
        # 返回昨天作为最新交易日
        return datetime.now() - timedelta(days=1)
    
    def batch_get_latest_trade_dates(self, stock_codes: list) -> dict:
        return {code: datetime.now() - timedelta(days=1) for code in stock_codes}
    
    def save_daily_data(self, daily_data: list) -> bool:
        for data in daily_data:
            stock_code = data['stock_code']
            if stock_code not in self.saved_data:
                self.saved_data[stock_code] = []
            self.saved_data[stock_code].append(data)
        return True
    
    def get_missing_dates(self, stock_code: str, start_date: datetime, end_date: datetime) -> list:
        # 模拟缺失日期
        return [start_date + timedelta(days=i) for i in range(3)]
    
    def batch_get_missing_dates(self, stock_codes: list, start_date: datetime, end_date: datetime) -> dict:
        return {code: [start_date + timedelta(days=i) for i in range(3)] for code in stock_codes}
    
    # 实现其他抽象方法（测试用简单实现）
    def close_connection(self):
        pass
    
    def get_selection_results(self, strategy_name: str, start_date: datetime, end_date: datetime) -> list:
        return []
    
    def get_stock_data(self, stock_code: str, start_date: datetime, end_date: datetime) -> list:
        return []
    
    def get_stock_info(self, stock_code: str) -> dict:
        return {}
    
    def init_database(self):
        pass
    
    def save_selection_result(self, result: dict) -> bool:
        return True
    
    def save_stock_info(self, stock_info: dict) -> bool:
        return True
    
    def save_stock_info_batch(self, stock_infos: list) -> bool:
        return True

def test_data_fetch():
    """测试数据获取功能"""
    try:
        logger.info("开始测试数据获取功能...")
        
        # 初始化数据源和数据访问
        data_source = TushareDataSource()
        data_access = MockDataAccess()
        data_service = OptimizedDataService(data_source, data_access)
        
        logger.info("测试增量数据更新...")
        incremental_success = data_service.update_incremental_data_optimized()
        logger.info(f"增量数据更新结果: {'成功' if incremental_success else '失败'}")
        
        # 检查保存的数据
        for stock_code in data_access.stock_codes:
            if stock_code in data_access.saved_data:
                logger.info(f"股票 {stock_code} 保存了 {len(data_access.saved_data[stock_code])} 条数据")
            else:
                logger.warning(f"股票 {stock_code} 未保存任何数据")
        
        logger.info("测试历史数据补充...")
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now() - timedelta(days=1)
        historical_success = data_service.update_historical_data_optimized(start_date, end_date)
        logger.info(f"历史数据补充结果: {'成功' if historical_success else '失败'}")
        
        # 检查保存的数据
        total_saved = 0
        for stock_code, data_list in data_access.saved_data.items():
            logger.info(f"股票 {stock_code} 共保存 {len(data_list)} 条数据")
            total_saved += len(data_list)
        logger.info(f"总共保存了 {total_saved} 条数据")
        
        logger.info("测试边界情况: 无效股票代码...")
        try:
            invalid_data = data_source.get_daily_data("123456", start_date, end_date)
            logger.info(f"无效股票代码返回数据: {len(invalid_data)}条")
        except DataSourceError as e:
            logger.error(f"无效股票代码测试失败: {str(e)}")
        
        logger.info("测试边界情况: 新股...")
        try:
            # 模拟新股（没有历史数据）
            data_access.get_latest_trade_date = lambda code: None
            new_stock_success = data_service.update_incremental_data_optimized()
            logger.info(f"新股更新结果: {'成功' if new_stock_success else '失败'}")
        except Exception as e:
            logger.error(f"新股测试失败: {str(e)}")
        
        logger.info("测试完成")
        return True
    
    except Exception as e:
        logger.exception("数据获取测试失败")
        return False

if __name__ == "__main__":
    test_data_fetch()
