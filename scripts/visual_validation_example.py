#!/usr/bin/env python3
"""
可视化策略验证示例脚本
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig
from src.utils.logger import setup_logger


def main():
    """主函数"""
    # 设置日志
    logger = setup_logger('visual_validation_example')

    try:
        # 创建数据访问实例
        data_access = MySQLDataAccess()

        # 创建可视化验证器
        visual_validator = VisualValidator(data_access)

        # 配置验证参数
        config = VisualValidationConfig(
            stock_code='601111',  # 中国国航
            strategy_name='technical_reversal',  # 技术反转策略
            start_date=datetime(2024, 3, 15),  # 开始日期
            end_date=datetime(2024, 5, 31),    # 结束日期
            chart_title='中国国航技术反转策略验证',
            show_indicators=True,
            show_volume=True,
            figure_size=(16, 12)
        )

        logger.info("开始可视化策略验证...")
        logger.info(f"股票代码: {config.stock_code}")
        logger.info(f"策略名称: {config.strategy_name}")
        logger.info(f"验证期间: {config.start_date.date()} 至 {config.end_date.date()}")

        # 执行可视化验证
        result = visual_validator.validate_stock_visual(config)

        # 输出结果
        if result.error_message:
            logger.error(f"验证失败: {result.error_message}")
            return

        logger.info("验证完成！")
        logger.info(f"股票名称: {result.stock_name}")
        logger.info(f"发现信号数量: {result.total_signals}")

        if result.chart_path:
            logger.info(f"图表已保存到: {result.chart_path}")

        # 输出验证总结
        print("\n" + "="*80)
        print(result.validation_summary)
        print("="*80)

        # 如果有信号点，显示详细信息
        if result.signal_points:
            print(f"\n发现 {len(result.signal_points)} 个策略信号点:")
            print("-" * 60)
            print(f"{'日期':<12} {'价格':<8} {'评分':<6} {'RSI':<6} {'量比':<6} {'原因'}")
            print("-" * 60)

            for signal in result.signal_points:
                print(f"{signal.date.date():<12} "
                      f"{signal.price:<8.2f} "
                      f"{signal.score:<6.1f} "
                      f"{signal.indicators.get('rsi', 0):<6.1f} "
                      f"{signal.indicators.get('volume_ratio', 0):<6.2f} "
                      f"{signal.reason}")
        else:
            print("\n在指定期间内未发现符合策略条件的信号点。")

        print(f"\n可视化验证完成！图表文件: {result.chart_path}")

    except Exception as e:
        logger.error(f"可视化验证失败: {str(e)}")
        raise


def test_multiple_stocks():
    """测试多只股票的可视化验证"""
    logger = setup_logger('visual_validation_multiple')

    # 测试股票列表
    test_stocks = [
        ('601111', '中国国航'),
        ('000001', '平安银行'),
        ('600036', '招商银行')
    ]

    try:
        data_access = MySQLDataAccess()
        visual_validator = VisualValidator(data_access)

        for stock_code, stock_name in test_stocks:
            logger.info(f"开始验证股票: {stock_name}({stock_code})")

            config = VisualValidationConfig(
                stock_code=stock_code,
                strategy_name='technical_reversal',
                start_date=datetime(2024, 4, 1),
                end_date=datetime(2024, 5, 31),
                chart_title=f'{stock_name}技术反转策略验证',
                show_indicators=True,
                show_volume=True
            )

            result = visual_validator.validate_stock_visual(config)

            if result.error_message:
                logger.warning(f"{stock_name} 验证失败: {result.error_message}")
                continue

            logger.info(f"{stock_name} 验证完成，发现 {result.total_signals} 个信号")
            if result.chart_path:
                logger.info(f"图表保存到: {result.chart_path}")

    except Exception as e:
        logger.error(f"批量验证失败: {str(e)}")


if __name__ == '__main__':
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == 'multiple':
        test_multiple_stocks()
    else:
        main()
