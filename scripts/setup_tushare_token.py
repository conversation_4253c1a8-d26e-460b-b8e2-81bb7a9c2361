#!/usr/bin/env python3
"""
Tushare Token配置脚本
用于设置和管理Tushare API Token
"""
import sys
import os
import getpass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config.tushare_config import tushare_config


def display_banner():
    """显示欢迎横幅"""
    print("=" * 60)
    print("🔧 Tushare Token 配置工具")
    print("=" * 60)
    print("此工具将帮助您配置Tushare API Token")
    print("Token将安全保存在本地配置文件中")
    print()


def get_token_from_user():
    """从用户输入获取token"""
    print("📝 请输入您的Tushare Token:")
    print("   1. 访问 https://tushare.pro/ 注册账号")
    print("   2. 登录后在个人中心获取Token")
    print("   3. Token格式类似: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
    print()
    
    # 使用getpass隐藏输入（可选）
    use_hidden_input = input("是否隐藏Token输入？(y/n，默认n): ").lower().strip()
    
    if use_hidden_input in ['y', 'yes']:
        token = getpass.getpass("请输入Token: ").strip()
    else:
        token = input("请输入Token: ").strip()
    
    return token


def validate_token_format(token):
    """验证token格式"""
    if not token:
        return False, "Token不能为空"
    
    if len(token) < 20:
        return False, "Token长度太短，请检查是否完整"
    
    if not token.replace('-', '').replace('_', '').isalnum():
        return False, "Token格式不正确，只能包含字母、数字、连字符和下划线"
    
    return True, "Token格式验证通过"


def test_token_connection(token):
    """测试token连接（简单验证）"""
    try:
        import tushare as ts
        ts.set_token(token)
        pro = ts.pro_api()
        
        # 尝试获取一个简单的数据来验证token
        df = pro.trade_cal(exchange='SSE', start_date='20240101', end_date='20240102')
        
        if df is not None and not df.empty:
            return True, "Token连接测试成功"
        else:
            return False, "Token连接测试失败：无法获取数据"
            
    except Exception as e:
        return False, f"Token连接测试失败: {str(e)}"


def show_current_config():
    """显示当前配置状态"""
    print("\n📋 当前配置状态:")
    print("-" * 40)
    
    if tushare_config.is_token_configured():
        token = tushare_config.get_token()
        masked_token = token[:8] + "*" * (len(token) - 12) + token[-4:] if len(token) > 12 else "*" * len(token)
        print(f"Token状态: ✅ 已配置")
        print(f"Token预览: {masked_token}")
    else:
        print(f"Token状态: ❌ 未配置")
    
    print(f"配置文件: {tushare_config.get_config_file_path()}")
    print(f"文件存在: {'✅ 是' if os.path.exists(tushare_config.get_config_file_path()) else '❌ 否'}")


def main_menu():
    """主菜单"""
    while True:
        print("\n🔧 请选择操作:")
        print("1. 设置新的Token")
        print("2. 查看当前配置")
        print("3. 测试Token连接")
        print("4. 删除Token配置")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            setup_token()
        elif choice == '2':
            show_current_config()
        elif choice == '3':
            test_current_token()
        elif choice == '4':
            delete_token()
        elif choice == '5':
            print("\n👋 再见！")
            break
        else:
            print("❌ 无效选项，请重新选择")


def setup_token():
    """设置token"""
    print("\n🔧 设置Tushare Token")
    print("-" * 40)
    
    # 获取token
    token = get_token_from_user()
    
    # 验证格式
    is_valid, message = validate_token_format(token)
    if not is_valid:
        print(f"❌ {message}")
        return
    
    print(f"✅ {message}")
    
    # 测试连接
    print("\n🔍 正在测试Token连接...")
    is_connected, message = test_token_connection(token)
    
    if not is_connected:
        print(f"⚠️  {message}")
        confirm = input("Token连接测试失败，是否仍要保存？(y/n): ").lower().strip()
        if confirm not in ['y', 'yes']:
            print("❌ 取消保存")
            return
    else:
        print(f"✅ {message}")
    
    # 保存token
    print("\n💾 正在保存Token...")
    if tushare_config.set_token(token):
        print("✅ Token保存成功！")
        print(f"📁 配置文件: {tushare_config.get_config_file_path()}")
    else:
        print("❌ Token保存失败")


def test_current_token():
    """测试当前token"""
    print("\n🔍 测试当前Token连接")
    print("-" * 40)
    
    if not tushare_config.is_token_configured():
        print("❌ 未配置Token，请先设置Token")
        return
    
    try:
        token = tushare_config.get_token()
        is_connected, message = test_token_connection(token)
        
        if is_connected:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


def delete_token():
    """删除token配置"""
    print("\n🗑️  删除Token配置")
    print("-" * 40)
    
    if not tushare_config.is_token_configured():
        print("❌ 未配置Token，无需删除")
        return
    
    confirm = input("⚠️  确定要删除Token配置吗？(y/n): ").lower().strip()
    if confirm not in ['y', 'yes']:
        print("❌ 取消删除")
        return
    
    try:
        config_file = tushare_config.get_config_file_path()
        if os.path.exists(config_file):
            os.remove(config_file)
            print("✅ Token配置已删除")
        else:
            print("❌ 配置文件不存在")
            
        # 重新加载配置
        tushare_config.token = tushare_config._load_token()
        
    except Exception as e:
        print(f"❌ 删除失败: {str(e)}")


def quick_setup():
    """快速设置模式（命令行参数）"""
    if len(sys.argv) > 1:
        token = sys.argv[1]
        
        print("🚀 快速设置模式")
        print("-" * 40)
        
        # 验证格式
        is_valid, message = validate_token_format(token)
        if not is_valid:
            print(f"❌ {message}")
            return False
        
        # 测试连接
        print("🔍 正在测试Token连接...")
        is_connected, message = test_token_connection(token)
        
        if not is_connected:
            print(f"⚠️  {message}")
        else:
            print(f"✅ {message}")
        
        # 保存token
        if tushare_config.set_token(token):
            print("✅ Token设置成功！")
            return True
        else:
            print("❌ Token保存失败")
            return False
    
    return None


def main():
    """主函数"""
    display_banner()
    
    # 检查是否是快速设置模式
    quick_result = quick_setup()
    if quick_result is not None:
        sys.exit(0 if quick_result else 1)
    
    # 显示当前状态
    show_current_config()
    
    # 进入主菜单
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作，再见！")
    except Exception as e:
        print(f"\n❌ 程序错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
