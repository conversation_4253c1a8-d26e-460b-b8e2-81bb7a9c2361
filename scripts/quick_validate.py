#!/usr/bin/env python3
"""
一键股票策略验证脚本
最简单的使用方式
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.visual_validator import VisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig


def quick_validate(stock_code, start_date, end_date):
    """
    一键验证函数
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    """
    try:
        print(f"🎯 验证 {stock_code} ({start_date} 至 {end_date})")
        
        # 创建验证器
        data_access = MySQLDataAccess()
        validator = VisualValidator(data_access)
        
        # 配置参数
        config = VisualValidationConfig(
            stock_code=stock_code,
            strategy_name='technical_reversal',
            start_date=datetime.strptime(start_date, '%Y-%m-%d'),
            end_date=datetime.strptime(end_date, '%Y-%m-%d'),
            show_indicators=True,
            show_volume=True
        )
        
        # 执行验证
        result = validator.validate_stock_visual(config)
        
        if result.error_message:
            print(f"❌ 失败: {result.error_message}")
        else:
            print(f"✅ 成功: 发现 {result.total_signals} 个信号")
            print(f"📊 图表: {result.chart_path}")
            
            # 显示信号
            for i, signal in enumerate(result.signal_points, 1):
                print(f"  {i}. {signal.date.date()} 价格:{signal.price:.2f} 评分:{signal.score:.1f}")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")


if __name__ == '__main__':
    if len(sys.argv) != 4:
        print("使用方法: python scripts/quick_validate.py <股票代码> <开始日期> <结束日期>")
        print("示例: python scripts/quick_validate.py 601111 2024-04-01 2024-04-30")
    else:
        quick_validate(sys.argv[1], sys.argv[2], sys.argv[3])
