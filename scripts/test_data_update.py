#!/usr/bin/env python3
"""
测试增量和历史数据获取功能
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.akshare_source import AkshareDataSource
from src.data.access.data_access_factory import DataAccessFactory
from src.utils.logger import setup_logger


def test_trading_calendar():
    """测试交易日历功能"""
    print("=" * 50)
    print("测试交易日历功能")
    print("=" * 50)
    
    try:
        data_source = AkshareDataSource()
        
        # 测试获取最近10天的交易日历
        end_date = datetime.now()
        start_date = end_date - timedelta(days=10)
        
        print(f"获取交易日历：{start_date.date()} 到 {end_date.date()}")
        
        trading_dates = data_source.get_trading_calendar(start_date, end_date)
        
        print(f"交易日数量: {len(trading_dates)}")
        print("交易日列表:")
        for date in trading_dates:
            print(f"  {date.strftime('%Y-%m-%d %A')}")
        
        return True
        
    except Exception as e:
        print(f"测试交易日历功能失败: {str(e)}")
        return False


def test_missing_dates():
    """测试缺失日期检测功能"""
    print("\n" + "=" * 50)
    print("测试缺失日期检测功能")
    print("=" * 50)
    
    try:
        data_access = DataAccessFactory.create_data_access()
        
        # 获取一个股票代码进行测试
        stock_codes = data_access.get_all_stock_codes()
        if not stock_codes:
            print("没有找到股票数据，请先运行股票列表更新")
            return False
        
        test_stock = stock_codes[0]
        print(f"测试股票: {test_stock}")
        
        # 测试最近30天的缺失日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        print(f"检查时间范围：{start_date.date()} 到 {end_date.date()}")
        
        missing_dates = data_access.get_missing_dates(test_stock, start_date, end_date)
        
        print(f"缺失日期数量: {len(missing_dates)}")
        if missing_dates:
            print("缺失的日期:")
            for date in missing_dates[:10]:  # 只显示前10个
                print(f"  {date.strftime('%Y-%m-%d %A')}")
            if len(missing_dates) > 10:
                print(f"  ... 还有 {len(missing_dates) - 10} 个日期")
        else:
            print("数据完整，无缺失日期")
        
        return True
        
    except Exception as e:
        print(f"测试缺失日期检测功能失败: {str(e)}")
        return False


def test_stocks_need_update():
    """测试需要更新的股票检测功能"""
    print("\n" + "=" * 50)
    print("测试需要更新的股票检测功能")
    print("=" * 50)
    
    try:
        data_access = DataAccessFactory.create_data_access()
        
        # 获取需要更新的股票列表
        stocks_need_update = data_access.get_stocks_need_update()
        
        print(f"需要更新的股票数量: {len(stocks_need_update)}")
        
        if stocks_need_update:
            print("需要更新的股票（前10个）:")
            for i, stock_code in enumerate(stocks_need_update[:10], 1):
                # 获取股票信息
                stock_info = data_access.get_stock_info(stock_code)
                latest_date = data_access.get_latest_trade_date(stock_code)
                
                stock_name = stock_info['stock_name'] if stock_info else "未知"
                latest_str = latest_date.strftime('%Y-%m-%d') if latest_date else "无数据"
                
                print(f"  {i}. {stock_code} - {stock_name}: 最新数据 {latest_str}")
            
            if len(stocks_need_update) > 10:
                print(f"  ... 还有 {len(stocks_need_update) - 10} 只股票需要更新")
        else:
            print("所有股票数据都是最新的")
        
        return True
        
    except Exception as e:
        print(f"测试需要更新的股票检测功能失败: {str(e)}")
        return False


def test_incremental_update():
    """测试增量更新功能（仅测试逻辑，不实际获取数据）"""
    print("\n" + "=" * 50)
    print("测试增量更新功能（逻辑测试）")
    print("=" * 50)
    
    try:
        data_source = AkshareDataSource()
        data_access = DataAccessFactory.create_data_access()
        
        # 获取需要更新的股票列表
        stocks_need_update = data_access.get_stocks_need_update()
        
        print(f"检测到 {len(stocks_need_update)} 只股票需要更新")
        
        if stocks_need_update:
            # 只测试第一只股票的增量更新逻辑
            test_stock = stocks_need_update[0]
            print(f"测试股票: {test_stock}")
            
            # 获取该股票的最新交易日期
            latest_date = data_access.get_latest_trade_date(test_stock)
            
            if latest_date:
                start_date = latest_date + timedelta(days=1)
                print(f"最新数据日期: {latest_date.strftime('%Y-%m-%d')}")
                print(f"增量更新起始日期: {start_date.strftime('%Y-%m-%d')}")
            else:
                start_date = datetime.now() - timedelta(days=30)
                print("该股票无历史数据")
                print(f"将获取最近30天数据，起始日期: {start_date.strftime('%Y-%m-%d')}")
            
            end_date = datetime.now()
            print(f"增量更新结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            # 检查是否需要实际更新
            if start_date.date() >= end_date.date():
                print("数据已是最新，无需更新")
            else:
                print("需要进行增量更新")
                print("（实际数据获取已跳过，仅测试逻辑）")
        
        return True
        
    except Exception as e:
        print(f"测试增量更新功能失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("开始测试数据更新功能...")
    
    # 设置日志
    logger = setup_logger(
        name="test_data_update",
        log_level="INFO",
        log_file="logs/test_data_update.log"
    )
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    tests = [
        ("交易日历功能", test_trading_calendar),
        ("缺失日期检测功能", test_missing_dates),
        ("需要更新的股票检测功能", test_stocks_need_update),
        ("增量更新功能", test_incremental_update),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 时发生异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    success_count = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("❌ 部分测试失败，请检查日志")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
