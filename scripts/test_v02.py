#!/usr/bin/env python3
"""
v0.2版本测试脚本 - 选股策略测试
"""
import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.access.data_access_factory import DataAccessFactory
from src.strategies.strategy_manager import StrategyManager
from src.strategies.volume_anomaly_strategy import VolumeAnomalyStrategy
from src.utils.logger import setup_logger


def test_volume_anomaly_strategy():
    """测试交易量异动策略"""
    print("=" * 60)
    print("测试交易量异动策略")
    print("=" * 60)
    
    try:
        # 创建策略实例
        strategy = VolumeAnomalyStrategy()
        
        # 测试策略基本信息
        print(f"策略名称: {strategy.get_strategy_name()}")
        print(f"策略描述: {strategy.get_strategy_description()}")
        
        # 测试配置
        config = strategy.get_config()
        print(f"\n默认配置:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # 测试配置验证
        test_config = {
            'volume_multiplier_threshold': 3.0,
            'min_price': 5.0,
            'max_price': 50.0,
            'baseline_days': 10,
            'exclude_st': True,
            'min_volume': 500000,
            'max_results': 10
        }
        
        is_valid = strategy.validate_config(test_config)
        print(f"\n测试配置验证: {'通过' if is_valid else '失败'}")
        
        if is_valid:
            strategy.set_config(test_config)
            print("配置已更新")
        
        print("✓ 交易量异动策略测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 交易量异动策略测试失败: {str(e)}")
        return False


def test_strategy_manager():
    """测试策略管理器"""
    print("\n" + "=" * 60)
    print("测试策略管理器")
    print("=" * 60)
    
    try:
        # 创建数据访问对象（使用SQLite进行测试）
        data_access = DataAccessFactory.create_data_access('sqlite')
        if not data_access:
            raise Exception("无法创建数据访问对象")
        
        # 创建策略管理器
        manager = StrategyManager(data_access)
        
        # 测试策略注册
        strategies = manager.get_available_strategies()
        print(f"可用策略: {strategies}")
        
        # 测试策略创建
        strategy = manager.create_strategy('volume_anomaly')
        if strategy:
            print(f"✓ 成功创建策略: {strategy.get_strategy_name()}")
        else:
            raise Exception("创建策略失败")
        
        # 测试策略信息获取
        info = manager.get_strategy_info('volume_anomaly')
        if info:
            print(f"策略信息: {info['name']} - {info['description']}")
        
        print("✓ 策略管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 策略管理器测试失败: {str(e)}")
        return False


def test_selection_execution():
    """测试选股执行（使用少量数据）"""
    print("\n" + "=" * 60)
    print("测试选股执行")
    print("=" * 60)
    
    try:
        # 创建数据访问对象
        data_access = DataAccessFactory.create_data_access('sqlite')
        if not data_access:
            raise Exception("无法创建数据访问对象")
        
        # 检查是否有数据
        stock_codes = data_access.get_all_stock_codes()
        print(f"数据库中股票数量: {len(stock_codes)}")
        
        if len(stock_codes) == 0:
            print("⚠ 数据库中没有股票数据，跳过选股执行测试")
            print("请先运行数据获取功能添加股票数据")
            return True
        
        # 创建策略管理器
        manager = StrategyManager(data_access)
        
        # 配置策略（降低阈值以便测试）
        test_config = {
            'volume_multiplier_threshold': 1.5,  # 降低阈值
            'min_price': 1.0,
            'max_price': 200.0,
            'baseline_days': 5,  # 减少基准天数
            'exclude_st': True,
            'min_volume': 100000,  # 降低最小交易量
            'max_results': 5  # 限制结果数量
        }
        
        print("开始执行选股策略...")
        print("配置参数:")
        for key, value in test_config.items():
            print(f"  {key}: {value}")
        
        # 执行策略（不保存结果）
        results = manager.execute_strategy('volume_anomaly', test_config, save_results=False)
        
        print(f"\n选股结果: 共选中 {len(results)} 只股票")
        
        if results:
            print("\n前3只股票:")
            for i, result in enumerate(results[:3], 1):
                print(f"{i}. {result['stock_code']} - {result['stock_name']}")
                print(f"   评分: {result['score']:.2f}")
                print(f"   原因: {result['reason']}")
        
        print("✓ 选股执行测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 选股执行测试失败: {str(e)}")
        return False


def test_result_formatting():
    """测试结果格式化"""
    print("\n" + "=" * 60)
    print("测试结果格式化")
    print("=" * 60)
    
    try:
        # 创建数据访问对象
        data_access = DataAccessFactory.create_data_access('sqlite')
        manager = StrategyManager(data_access)
        
        # 创建模拟结果
        mock_results = [
            {
                'stock_code': '000001',
                'stock_name': '平安银行',
                'score': 85.5,
                'reason': '交易量放大3.2倍，当前价格12.50元',
                'selection_date': datetime.now().date(),
                'volume_multiplier': 3.2,
                'close_price': 12.50,
                'latest_volume': 5000000,
                'execution_time': 2.5
            },
            {
                'stock_code': '000002',
                'stock_name': '万科A',
                'score': 78.3,
                'reason': '交易量放大2.8倍，当前价格25.30元',
                'selection_date': datetime.now().date(),
                'volume_multiplier': 2.8,
                'close_price': 25.30,
                'latest_volume': 3200000,
                'execution_time': 2.5
            }
        ]
        
        # 测试基本格式化
        formatted = manager.format_results(mock_results, show_details=False)
        print("基本格式化结果:")
        print(formatted)
        
        # 测试详细格式化
        print("\n详细格式化结果:")
        formatted_detailed = manager.format_results(mock_results, show_details=True)
        print(formatted_detailed)
        
        print("✓ 结果格式化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 结果格式化测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("A股智能选股系统 v0.2 版本测试")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 设置日志
    logger = setup_logger('test_v02', 'logs/test_v02.log')
    logger.info("开始v0.2版本测试")
    
    # 执行测试
    tests = [
        ("交易量异动策略", test_volume_anomaly_strategy),
        ("策略管理器", test_strategy_manager),
        ("选股执行", test_selection_execution),
        ("结果格式化", test_result_formatting)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {str(e)}")
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！v0.2版本功能正常")
        logger.info("v0.2版本测试全部通过")
        return True
    else:
        print(f"\n⚠ 有{total - passed}个测试失败，请检查问题")
        logger.warning(f"v0.2版本测试有{total - passed}个失败")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
